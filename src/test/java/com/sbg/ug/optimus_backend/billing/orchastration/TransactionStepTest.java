package com.sbg.ug.optimus_backend.billing.orchastration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.function.Consumer;
import java.util.function.Function;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TransactionStepTest {

    @Mock private DistributedAction<String> mockAction;

    @Mock private Function<TransactionContext, Boolean> mockCondition;

    @Mock private Consumer<Exception> mockErrorHandler;

    @Test
    void shouldBuildTransactionStepWithAllProperties() {
        // Arrange
        String stepName = "testStep";
        RetryPolicy customRetryPolicy = new RetryPolicy(5, 2000, 1.5, 20000);

        // Act
        TransactionStep<String> step =
                new TransactionStep.Builder<String>()
                        .name(stepName)
                        .action(mockAction)
                        .condition(mockCondition)
                        .retryPolicy(customRetryPolicy)
                        .onError(mockErrorHandler)
                        .build();

        // Assert
        assertEquals(stepName, step.getName());
        assertSame(mockAction, step.getAction());
        assertSame(mockCondition, step.getCondition());
        assertSame(customRetryPolicy, step.getRetryPolicy());
        assertSame(mockErrorHandler, step.getErrorHandler());
    }

    @Test
    void shouldUseDefaultValuesWhenNotSpecified() {
        // Arrange
        String stepName = "defaultValuesStep";

        // Act
        TransactionStep<String> step =
                new TransactionStep.Builder<String>().name(stepName).action(mockAction).build();

        // Assert
        assertEquals(stepName, step.getName());
        assertSame(mockAction, step.getAction());
        assertNotNull(step.getCondition());
        assertNotNull(step.getRetryPolicy());
        assertNotNull(step.getErrorHandler());

        // Verify default retry policy matches expected values
        RetryPolicy defaultPolicy = RetryPolicy.defaultPolicy();
        assertEquals(defaultPolicy.maxAttempts(), step.getRetryPolicy().maxAttempts());
        assertEquals(defaultPolicy.initialDelay(), step.getRetryPolicy().initialDelay());
        assertEquals(defaultPolicy.backoffMultiplier(), step.getRetryPolicy().backoffMultiplier());
        assertEquals(defaultPolicy.maxDelay(), step.getRetryPolicy().maxDelay());
    }

    @Test
    void shouldEvaluateDefaultConditionToTrue() {
        // Arrange
        TransactionStep<String> step =
                new TransactionStep.Builder<String>()
                        .name("conditionTestStep")
                        .action(mockAction)
                        .build();

        TransactionContext context = new TransactionContext();

        // Act
        Boolean result = step.getCondition().apply(context);

        // Assert
        assertTrue(result, "Default condition should evaluate to true");
    }

    @Test
    void shouldUseCustomCondition() {
        // Arrange
        when(mockCondition.apply(any())).thenReturn(false);

        TransactionStep<String> step =
                new TransactionStep.Builder<String>()
                        .name("customConditionStep")
                        .action(mockAction)
                        .condition(mockCondition)
                        .build();

        TransactionContext context = new TransactionContext();

        // Act
        Boolean result = step.getCondition().apply(context);

        // Assert
        assertFalse(result, "Custom condition should be used");
        verify(mockCondition).apply(context);
    }

    @Test
    void shouldHandleNullParametersGracefully() {
        // Arrange & Act
        TransactionStep<String> step =
                new TransactionStep.Builder<String>()
                        .name(null)
                        .action(null)
                        .condition(null)
                        .retryPolicy(null)
                        .onError(null)
                        .build();

        // Assert - should not throw exceptions
        assertNull(step.getName());
        assertNull(step.getAction());
        assertNull(step.getCondition());
        assertNull(step.getRetryPolicy());
        assertNull(step.getErrorHandler());
    }

    @Test
    void shouldAllowChainedBuilderMethods() {
        // Act
        TransactionStep<String> step =
                new TransactionStep.Builder<String>()
                        .name("chainedStep")
                        .action(mockAction)
                        .condition(ctx -> false)
                        .retryPolicy(new RetryPolicy(1, 500, 1.0, 1000))
                        .onError(ex -> System.out.println("Error occurred"))
                        .build();

        // Assert
        assertEquals("chainedStep", step.getName());
        assertSame(mockAction, step.getAction());
        assertNotNull(step.getCondition());
        assertFalse(step.getCondition().apply(new TransactionContext()));
        assertEquals(1, step.getRetryPolicy().maxAttempts());
        assertEquals(500, step.getRetryPolicy().initialDelay());
    }
}
