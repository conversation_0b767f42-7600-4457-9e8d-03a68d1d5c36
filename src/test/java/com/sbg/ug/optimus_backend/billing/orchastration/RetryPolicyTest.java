package com.sbg.ug.optimus_backend.billing.orchastration;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

class RetryPolicyTest {

    @Test
    void testConstructorAndAccessors() {
        // Arrange & Act
        RetryPolicy policy = new RetryPolicy(5, 2000, 3.0, 20000);

        // Assert
        assertEquals(5, policy.maxAttempts(), "maxAttempts should match constructor value");
        assertEquals(2000, policy.initialDelay(), "initialDelay should match constructor value");
        assertEquals(
                3.0,
                policy.backoffMultiplier(),
                "backoffMultiplier should match constructor value");
        assertEquals(20000, policy.maxDelay(), "maxDelay should match constructor value");
    }

    @Test
    void testDefaultPolicy() {
        // Act
        RetryPolicy defaultPolicy = RetryPolicy.defaultPolicy();

        // Assert
        assertEquals(3, defaultPolicy.maxAttempts(), "Default maxAttempts should be 3");
        assertEquals(1000, defaultPolicy.initialDelay(), "Default initialDelay should be 1000ms");
        assertEquals(
                2.0, defaultPolicy.backoffMultiplier(), "Default backoffMultiplier should be 2.0");
        assertEquals(10000, defaultPolicy.maxDelay(), "Default maxDelay should be 10000ms");
    }

    @Test
    void testEquality() {
        // Arrange
        RetryPolicy policy1 = new RetryPolicy(3, 1000, 2.0, 10000);
        RetryPolicy policy2 = new RetryPolicy(3, 1000, 2.0, 10000);
        RetryPolicy policy3 = new RetryPolicy(5, 2000, 3.0, 20000);

        // Assert
        assertEquals(policy1, policy2, "Identical policies should be equal");
        assertNotEquals(policy1, policy3, "Different policies should not be equal");
        assertEquals(
                policy1,
                RetryPolicy.defaultPolicy(),
                "Policy with default values should equal defaultPolicy()");
    }

    @Test
    void testHashCode() {
        // Arrange
        RetryPolicy policy1 = new RetryPolicy(3, 1000, 2.0, 10000);
        RetryPolicy policy2 = new RetryPolicy(3, 1000, 2.0, 10000);
        RetryPolicy policy3 = new RetryPolicy(5, 2000, 3.0, 20000);

        // Assert
        assertEquals(
                policy1.hashCode(),
                policy2.hashCode(),
                "Hash codes should be equal for identical policies");
        assertNotEquals(
                policy1.hashCode(),
                policy3.hashCode(),
                "Hash codes should differ for different policies");
    }

    @Test
    void testToString() {
        // Arrange
        RetryPolicy policy = new RetryPolicy(5, 2000, 3.0, 20000);

        // Act
        String toString = policy.toString();

        // Assert
        assertTrue(toString.contains("maxAttempts=5"), "toString should contain maxAttempts value");
        assertTrue(
                toString.contains("initialDelay=2000"),
                "toString should contain initialDelay value");
        assertTrue(
                toString.contains("backoffMultiplier=3.0"),
                "toString should contain backoffMultiplier value");
        assertTrue(toString.contains("maxDelay=20000"), "toString should contain maxDelay value");
    }
}
