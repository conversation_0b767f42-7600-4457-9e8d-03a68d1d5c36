package com.sbg.ug.optimus_backend.billing.usecase;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.sbg.ug.optimus_backend.billing.external.http.ApiConnectClient;
import com.sbg.ug.optimus_backend.billing.external.http.data.ListBillersDto;
import com.sbg.ug.optimus_backend.billing.external.http.data.ValidateRefNumberDto;
import com.sbg.ug.optimus_backend.billing.usecase.data.NotifyPaymentCmd;
import com.sbg.ug.optimus_backend.billing.usecase.data.PaymentIdCheck;
import com.sbg.ug.optimus_backend.billing.usecase.services.BillerService;
import com.sbg.ug.optimus_backend.billing.usecase.services.PaymentService;
import com.sbg.ug.optimus_backend.finacle.spi.FinaclePaymentSpi;
import com.sbg.ug.optimus_backend.finacle.usecase.data.PayToAccountCmd;
import com.sbg.ug.optimus_backend.finacle.usecase.data.PayToManyAccountsCmd;
import com.sbg.ug.optimus_backend.otp.spi.SmsOtpSpi;
import com.sbg.ug.optimus_backend.testutil.DevJsonUtil;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class NotifyPaymentUseCaseTest {

    @Mock private FinaclePaymentSpi finaclePaymentSpi;

    @Mock private ApiConnectClient apiConnectClient;

    @Mock private BillerService billerService;

    @Mock private PaymentService paymentService;

    @Mock private SmsOtpSpi smsOtpSpi;

    @InjectMocks private NotifyPaymentUseCase notifyPaymentUseCase;

    @Captor private ArgumentCaptor<PayToAccountCmd.Req> payToAccountCaptor;

    @Captor private ArgumentCaptor<PayToManyAccountsCmd.Req> payToManyAccountsCaptor;
    @Captor private ArgumentCaptor<ValidateRefNumberDto.Req> validateRefNumberCaptor;

    ListBillersDto.Resp billerCatalog =
            DevJsonUtil.readJsonFrmPath(
                    "json/billers/full-biller-list-valid.json", ListBillersDto.Resp.class);

    private NotifyPaymentCmd.Req validRequest;
    private PayToAccountCmd.Resp successPaymentResponse;
    private PayToManyAccountsCmd.Resp successPaymentToManyAccountsResponse;
    private final Map<String, Object> billerCharges = new HashMap<>();
    private ValidateRefNumberDto.Resp validateCustomerRefResp;

    @BeforeEach
    void setUp() {
        // Set up a valid payment request
        Map<String, Object> otherFields = new HashMap<>();
        otherFields.put("packageId", "New Connection");
        otherFields.put("phoneNumber", "**********");

        validRequest =
                new NotifyPaymentCmd.Req(
                        "UMEME", // billerId
                        "postPayment", // stepId
                        new BigDecimal("50000"), // amount
                        "*************", // debitAccount
                        "*************", // customerReference
                        "5f504edc-34bf-433e-a9a9-2e58e3ce0c05",
                        "25678", // paymentId
                        otherFields,
                        getBillerValidationReq(),
                        "John Doe",
                        "**********");

        validateCustomerRefResp =
                new ValidateRefNumberDto.Resp(
                        new BigDecimal("10000"),
                        "description",
                        "*************",
                        "UGX",
                        "equalorgreater",
                        false,
                        "*********",
                        new BigDecimal("200"),
                        List.of("info"));

        // Set up a successful payment response
        var paymentResponse =
                new PayToAccountCmd.Resp(
                        "200",
                        "Successful",
                        "20250527131730OalGSy",
                        "2025-05-27T10:16:23.911",
                        "S55368884");

        var paymentToManyAccountsResponse =
                new PayToManyAccountsCmd.Resp(
                        "200",
                        "Successful",
                        "20250527131730OalGSy",
                        "2025-05-27T10:16:23.911",
                        "S55368884");

        successPaymentResponse = paymentResponse;
        successPaymentToManyAccountsResponse = paymentToManyAccountsResponse;

        billerCharges.put("ExciseDuty", 15);
        billerCharges.put("MainTransactionCharge", 1000);
        billerCharges.put("DebitNarration", "************* UMEME 109384848y638400");
        billerCharges.put("CreditNarration", "************* UMEME 109384848y638400");
        billerCharges.put("Excise", "031100UGX5215050");
        billerCharges.put("Revenue", "031100UGX8024001");
    }

    private static NotifyPaymentCmd.Req.ValidationRequest getBillerValidationReq() {
        return new NotifyPaymentCmd.Req.ValidationRequest(
                "UMEME", "*************", "validation", Map.of());
    }

    //    @Test
    //    void shouldTriggerChargePostingWhenMainTransactionChargeIsNonZero() throws Exception {
    //        // Arrange
    //        Map<String, Object> mockCharges = new HashMap<>();
    //        mockCharges.put("MainTransactionCharge", 0); // or Integer.valueOf(100)
    //        mockCharges.put("ExciseDuty", "10");
    //
    //        when(billerService.getBillerCharges(any(), any(), any())).thenReturn(mockCharges);
    //
    //        // Mock other dependencies as needed
    //        // e.g., when(finaclePaymentSpi.payToAccount(...)).thenReturn(...);
    //
    //        NotifyPaymentCmd.Req req = mock(NotifyPaymentCmd.Req.class);
    //        when(req.billerId()).thenReturn("BILLER123");
    //        when(req.customerReference()).thenReturn("REF123");
    //
    //        // Act
    //        notifyPaymentUseCase.notifyPayment(req);
    //
    //        // Assert
    //        verify(finaclePaymentSpi).payToManyAccounts(any()); // This should be called if charge
    // != 0
    //    }

    @Test
    void shouldNotTriggerChargePostingWhenMainTransactionChargeIsZero() throws Exception {
        // Arrange
        Map<String, Object> zeroChargeMap = new HashMap<>();
        zeroChargeMap.put("MainTransactionCharge", 0);
        zeroChargeMap.put("ExciseDuty", "10");

        when(billerService.getSupportedBillers("UNSUPPORTEDBILLERS", billerCatalog))
                .thenReturn(billerCatalog);
        when(apiConnectClient.listBillers()).thenReturn(billerCatalog);
        when(apiConnectClient.validateReferenceNumber(any())).thenReturn(validateCustomerRefResp);
        when(finaclePaymentSpi.payToAccount(any())).thenReturn(successPaymentResponse);
        when(paymentService.checkPaymentId(any()))
                .thenReturn(
                        new PaymentIdCheck(false, new NotifyPaymentCmd.Resp("", "", "", "", "")));
        doNothing().when(smsOtpSpi).validateSmsOtp(any(), any());
        when(billerService.getBillerCharges("BILLERCHARGES", "UMEME", "*************"))
                .thenReturn(zeroChargeMap);
        when(billerService.generateNarrations(
                        eq("NARRATIONS"),
                        eq("UMEME"),
                        eq("*************"),
                        anyString(),
                        eq("John Doe"),
                        eq("New Connection"),
                        eq("**********")))
                .thenReturn(
                        Map.of(
                                "DebitNarration", "************* UMEME 109384848y638400",
                                "CreditNarration", "************* UMEME 109384848y638400",
                                "BillerNarration", "UMEME Payment"));

        // Act
        NotifyPaymentCmd.Resp result = notifyPaymentUseCase.notifyPayment(validRequest);

        // Assert
        assertNotNull(result);
        assertEquals("200", result.statusCode());
        verify(finaclePaymentSpi, never()).payToManyAccounts(any());
    }

    @Test
    void shouldSuccessfullyNotifyPaymentWithBillerNotSupposedToBeCharged() throws Exception {
        // Given
        when(billerService.getSupportedBillers("UNSUPPORTEDBILLERS", billerCatalog))
                .thenReturn(billerCatalog);
        when(apiConnectClient.listBillers()).thenReturn(billerCatalog);
        when(apiConnectClient.validateReferenceNumber(any())).thenReturn(validateCustomerRefResp);
        when(finaclePaymentSpi.payToAccount(any())).thenReturn(successPaymentResponse);

        when(paymentService.checkPaymentId(any()))
                .thenReturn(
                        new PaymentIdCheck(false, new NotifyPaymentCmd.Resp("", "", "", "", "")));

        doNothing().when(smsOtpSpi).validateSmsOtp(any(), any());

        when(billerService.getBillerCharges("BILLERCHARGES", "TOTALBILLS", "*************"))
                .thenReturn(billerCharges);
        var totalbillsreq =
                new NotifyPaymentCmd.Req(
                        "TOTALBILLS", // billerId
                        "postPayment", // stepId
                        new BigDecimal("50000"), // amount
                        "*************", // debitAccount
                        "*************", // customerReference
                        "5f504edc-34bf-433e-a9a9-2e58e3ce0c05",
                        "25678", // paymentId
                        Map.of(),
                        getBillerValidationReq(),
                        "John Doe",
                        "**********");
        // When
        NotifyPaymentCmd.Resp result = notifyPaymentUseCase.notifyPayment(totalbillsreq);

        assertEquals("TOTALBILLS", totalbillsreq.billerId());
        assertEquals("postPayment", totalbillsreq.stepId());

        // Then
        assertNotNull(result);
        assertEquals("200", result.statusCode());
        assertEquals("Successful", result.description());
        assertEquals("S55368884", result.transactionId());
        assertEquals("20250527131730OalGSy", result.requestId());
        assertEquals("2025-05-27T10:16:23.911", result.responseDateTime());

        // Verify interactions
        verify(apiConnectClient).listBillers();
        verify(apiConnectClient).validateReferenceNumber(validateRefNumberCaptor.capture());
        verify(finaclePaymentSpi).payToAccount(payToAccountCaptor.capture());
        verify(billerService).getSupportedBillers("UNSUPPORTEDBILLERS", billerCatalog);
        verify(smsOtpSpi)
                .validateSmsOtp("*************", "25678"); // Validate SMS OTP with account and OTP

        PayToAccountCmd.Req capturedReq = payToAccountCaptor.getValue();

        assertEquals(new BigDecimal("50000"), new BigDecimal(capturedReq.amount()));
        assertEquals("UGX", capturedReq.currency());
        assertEquals("*************", capturedReq.debit().accountNumber());
        assertEquals(new BigDecimal("50000"), new BigDecimal(capturedReq.amount()));
        assertEquals("UGX", capturedReq.currency());
        assertEquals("*************", capturedReq.debit().accountNumber());
    }

    @Test
    void shouldSuccessfullyNotifyPayment() throws Exception {
        // Given
        when(billerService.getSupportedBillers("UNSUPPORTEDBILLERS", billerCatalog))
                .thenReturn(billerCatalog);
        when(apiConnectClient.listBillers()).thenReturn(billerCatalog);
        when(apiConnectClient.validateReferenceNumber(any())).thenReturn(validateCustomerRefResp);
        when(finaclePaymentSpi.payToAccount(any())).thenReturn(successPaymentResponse);
        when(finaclePaymentSpi.payToManyAccounts(any()))
                .thenReturn(successPaymentToManyAccountsResponse);

        when(paymentService.checkPaymentId(any()))
                .thenReturn(
                        new PaymentIdCheck(false, new NotifyPaymentCmd.Resp("", "", "", "", "")));

        doNothing().when(smsOtpSpi).validateSmsOtp(any(), any());

        when(billerService.generateNarrations(
                        eq("NARRATIONS"),
                        eq("UMEME"),
                        eq("*************"),
                        anyString(),
                        eq("John Doe"),
                        eq("New Connection"),
                        eq("**********"))) // Use the phone number from otherFields
                .thenReturn(
                        Map.of(
                                "DebitNarration",
                                "************* UMEME 109384848y638400",
                                "CreditNarration",
                                "************* UMEME 109384848y638400",
                                "BillerNarration",
                                "UMEME Payment"));

        when(billerService.getBillerCharges("BILLERCHARGES", "UMEME", "*************"))
                .thenReturn(billerCharges);
        // When
        NotifyPaymentCmd.Resp result = notifyPaymentUseCase.notifyPayment(validRequest);

        var narrations =
                billerService.generateNarrations(
                        "NARRATIONS",
                        "UMEME",
                        "*************",
                        "109384848y638400",
                        "John Doe",
                        "New Connection",
                        "**********");

        // Verify the narrations
        assertEquals("************* UMEME 109384848y638400", narrations.get("DebitNarration"));
        assertEquals("************* UMEME 109384848y638400", narrations.get("CreditNarration"));
        assertEquals("UMEME Payment", narrations.get("BillerNarration"));
        assertEquals("UMEME", validRequest.billerId());
        assertEquals("postPayment", validRequest.stepId());
        assertEquals("John Doe", validRequest.customerName());
        assertEquals("**********", validRequest.phoneNumber());

        // Then
        assertNotNull(result);
        assertEquals("200", result.statusCode());
        assertEquals("Successful", result.description());
        assertEquals("S55368884", result.transactionId());
        assertEquals("20250527131730OalGSy", result.requestId());
        assertEquals("2025-05-27T10:16:23.911", result.responseDateTime());

        // Verify interactions
        verify(apiConnectClient).listBillers();
        verify(apiConnectClient).validateReferenceNumber(validateRefNumberCaptor.capture());
        verify(billerService)
                .generateNarrations(
                        "NARRATIONS",
                        "UMEME",
                        "*************",
                        "109384848y638400",
                        "John Doe",
                        "New Connection",
                        "**********");
        verify(finaclePaymentSpi).payToAccount(payToAccountCaptor.capture());
        verify(finaclePaymentSpi).payToManyAccounts(payToManyAccountsCaptor.capture());
        verify(billerService).getSupportedBillers("UNSUPPORTEDBILLERS", billerCatalog);
        verify(billerService).getBillerCharges("BILLERCHARGES", "UMEME", "*************");
        verify(smsOtpSpi)
                .validateSmsOtp("*************", "25678"); // Validate SMS OTP with account and OTP

        PayToAccountCmd.Req capturedReq = payToAccountCaptor.getValue();
        PayToManyAccountsCmd.Req capturedManyAccountsReq = payToManyAccountsCaptor.getValue();

        assertEquals(new BigDecimal("50000"), new BigDecimal(capturedReq.amount()));
        assertEquals("UGX", capturedReq.currency());
        assertEquals("*************", capturedReq.debit().accountNumber());
        assertEquals("1150.0", capturedManyAccountsReq.amount());
        assertEquals("UGX", capturedManyAccountsReq.currency());
        assertEquals("************* UMEME 109384848y638400", capturedReq.debit().narration());
        assertEquals(new BigDecimal("50000"), new BigDecimal(capturedReq.amount()));
        assertEquals("UGX", capturedReq.currency());
        assertEquals("*************", capturedReq.debit().accountNumber());

        assertEquals(new BigDecimal("1150.0"), new BigDecimal(capturedManyAccountsReq.amount()));
        assertEquals("UGX", capturedManyAccountsReq.currency());
        assertEquals("*************", capturedManyAccountsReq.debit().accountNumber());

        assertEquals("1150.0", capturedManyAccountsReq.amount());
        assertEquals("UGX", capturedManyAccountsReq.currency());
        assertEquals("DebitNarration", capturedManyAccountsReq.debit().narration());
    }

    @Test
    void shouldThrowExceptionWhenBillerNotFound() throws Exception {
        when(billerService.getSupportedBillers("UNSUPPORTEDBILLERS", billerCatalog))
                .thenReturn(billerCatalog);
        // Given
        when(apiConnectClient.listBillers()).thenReturn(billerCatalog);

        // Create request with invalid biller ID
        Map<String, Object> otherFields = new HashMap<>();
        otherFields.put("packageId", "New Connection");

        NotifyPaymentCmd.Req invalidBillerRequest =
                new NotifyPaymentCmd.Req(
                        "NON_EXISTENT_BILLER",
                        "payment",
                        new BigDecimal("50000"),
                        "*************",
                        "*************",
                        "25678",
                        "5f504edc-34bf-433e-a9a9-2e58e3ce0c05",
                        otherFields,
                        getBillerValidationReq(),
                        "John Doe",
                        "**********");

        // Then
        assertThrows(
                Exception.class, () -> notifyPaymentUseCase.notifyPayment(invalidBillerRequest));
        verify(billerService).getSupportedBillers("UNSUPPORTEDBILLERS", billerCatalog);
        verify(apiConnectClient).listBillers();
    }

    @Test
    void shouldCreateCorrectPayToAccountRequest() throws Exception {

        // Given
        when(billerService.getSupportedBillers("UNSUPPORTEDBILLERS", billerCatalog))
                .thenReturn(billerCatalog);
        when(finaclePaymentSpi.payToAccount(any())).thenReturn(successPaymentResponse);
        when(finaclePaymentSpi.payToManyAccounts(any()))
                .thenReturn(successPaymentToManyAccountsResponse);
        when(paymentService.checkPaymentId("5f504edc-34bf-433e-a9a9-2e58e3ce0c05"))
                .thenReturn(
                        new PaymentIdCheck(false, new NotifyPaymentCmd.Resp("", "", "", "", "")));
        // Given
        when(apiConnectClient.listBillers()).thenReturn(billerCatalog);
        when(apiConnectClient.validateReferenceNumber(any())).thenReturn(validateCustomerRefResp);
        when(finaclePaymentSpi.payToAccount(any())).thenReturn(successPaymentResponse);
        when(finaclePaymentSpi.payToManyAccounts(any(PayToManyAccountsCmd.Req.class)))
                .thenReturn(successPaymentToManyAccountsResponse);

        doNothing().when(smsOtpSpi).validateSmsOtp(any(), any());

        when(billerService.generateNarrations(
                        eq("NARRATIONS"),
                        eq("UMEME"),
                        eq("*************"),
                        anyString(),
                        eq("John Doe"),
                        eq("New Connection"),
                        eq("**********"))) // Use the phone number from otherFields
                .thenReturn(
                        Map.of(
                                "DebitNarration",
                                "************* UMEME 109384848y638400",
                                "CreditNarration",
                                "************* UMEME 109384848y638400",
                                "BillerNarration",
                                "UMEME Payment"));
        when(billerService.getBillerCharges("BILLERCHARGES", "UMEME", "*************"))
                .thenReturn(billerCharges);

        // When
        notifyPaymentUseCase.notifyPayment(validRequest);

        // Then
        verify(apiConnectClient).listBillers();
        verify(apiConnectClient).validateReferenceNumber(validateRefNumberCaptor.capture());
        verify(billerService).getSupportedBillers("UNSUPPORTEDBILLERS", billerCatalog);
        verify(billerService).getBillerCharges("BILLERCHARGES", "UMEME", "*************");
        verify(smsOtpSpi).validateSmsOtp("*************", "25678");
        verify(finaclePaymentSpi).payToAccount(payToAccountCaptor.capture());
        verify(finaclePaymentSpi).payToManyAccounts(payToManyAccountsCaptor.capture());

        PayToAccountCmd.Req capturedReq = payToAccountCaptor.getValue();
        PayToManyAccountsCmd.Req capturedManyAccountsReq = payToManyAccountsCaptor.getValue();

        // Assert payment request details
        assertEquals(validRequest.amount(), new BigDecimal(capturedReq.amount()));
        assertEquals("UGX", capturedReq.currency());
        assertEquals(validRequest.debitAccount(), capturedReq.debit().accountNumber());
        // Assert payment request details
        assertEquals(new BigDecimal("1150.0"), new BigDecimal(capturedManyAccountsReq.amount()));
        assertEquals("UGX", capturedManyAccountsReq.currency());
        assertEquals(validRequest.debitAccount(), capturedManyAccountsReq.debit().accountNumber());
    }

    @Test
    void shouldReturnSavedResponseIfPaymentIdExists() throws Exception {
        when(billerService.getSupportedBillers("UNSUPPORTEDBILLERS", billerCatalog))
                .thenReturn(billerCatalog);
        when(apiConnectClient.listBillers()).thenReturn(billerCatalog);
        when(paymentService.checkPaymentId("5f504edc-34bf-433e-a9a9-2e58e3ce0c05"))
                .thenReturn(
                        new PaymentIdCheck(
                                true,
                                new NotifyPaymentCmd.Resp(
                                        "S55421113",
                                        "20250529130351Riyy3j",
                                        "2025-05-29T10:03:56.138",
                                        "Successful",
                                        "200")));

        NotifyPaymentCmd.Resp result = notifyPaymentUseCase.notifyPayment(validRequest);

        assertNotNull(result);
        assertEquals("200", result.statusCode());
        assertEquals("Successful", result.description());
        assertEquals("20250529130351Riyy3j", result.requestId());
        assertEquals("2025-05-29T10:03:56.138", result.responseDateTime());
        assertEquals("S55421113", result.transactionId());
    }

    @Test
    void shouldReturnSuccessIfSavingPaymentIdFails() throws Exception {
        // Given
        when(billerService.getSupportedBillers("UNSUPPORTEDBILLERS", billerCatalog))
                .thenReturn(billerCatalog);
        when(apiConnectClient.listBillers()).thenReturn(billerCatalog);
        when(apiConnectClient.validateReferenceNumber(any())).thenReturn(validateCustomerRefResp);
        when(finaclePaymentSpi.payToAccount(any())).thenReturn(successPaymentResponse);
        when(paymentService.checkPaymentId("5f504edc-34bf-433e-a9a9-2e58e3ce0c05"))
                .thenReturn(
                        new PaymentIdCheck(false, new NotifyPaymentCmd.Resp("", "", "", "", "")));

        doNothing().when(smsOtpSpi).validateSmsOtp(any(), any());

        when(billerService.generateNarrations(
                        eq("NARRATIONS"),
                        eq("UMEME"),
                        eq("*************"),
                        anyString(),
                        eq("John Doe"),
                        eq("New Connection"),
                        eq("**********"))) // Use the phone number from otherFields
                .thenReturn(
                        Map.of(
                                "DebitNarration",
                                "************* UMEME 109384848y638400",
                                "CreditNarration",
                                "************* UMEME 109384848y638400",
                                "BillerNarration",
                                "UMEME Payment"));

        when(billerService.getBillerCharges("BILLERCHARGES", "UMEME", "*************"))
                .thenReturn(billerCharges);

        doThrow(new RuntimeException("DB failure"))
                .when(paymentService)
                .logPaymentId(any(), anyString());
        // When
        NotifyPaymentCmd.Resp result = notifyPaymentUseCase.notifyPayment(validRequest);

        var narrations =
                billerService.generateNarrations(
                        "NARRATIONS",
                        "UMEME",
                        "*************",
                        "109384848y638400",
                        "John Doe",
                        "New Connection",
                        "**********");

        // Verify the narrations
        assertEquals("************* UMEME 109384848y638400", narrations.get("DebitNarration"));
        assertEquals("************* UMEME 109384848y638400", narrations.get("CreditNarration"));
        assertEquals("UMEME Payment", narrations.get("BillerNarration"));
        assertEquals("UMEME", validRequest.billerId());
        assertEquals("postPayment", validRequest.stepId());

        // Then
        assertNotNull(result);
        assertEquals("200", result.statusCode());

        // Verify interactions
        verify(apiConnectClient).listBillers();
        verify(apiConnectClient).validateReferenceNumber(validateRefNumberCaptor.capture());
        verify(billerService)
                .generateNarrations(
                        "NARRATIONS",
                        "UMEME",
                        "*************",
                        "109384848y638400",
                        "John Doe",
                        "New Connection",
                        "**********");
        verify(finaclePaymentSpi).payToAccount(payToAccountCaptor.capture());
        verify(billerService).getSupportedBillers("UNSUPPORTEDBILLERS", billerCatalog);
        verify(billerService).getBillerCharges("BILLERCHARGES", "UMEME", "*************");
        verify(smsOtpSpi).validateSmsOtp("*************", "25678");

        // Verify payment request details
        PayToAccountCmd.Req capturedReq = payToAccountCaptor.getValue();
        ValidateRefNumberDto.Req capturedValidateRefNumberReq = validateRefNumberCaptor.getValue();

        assertEquals(new BigDecimal("50000"), new BigDecimal(capturedReq.amount()));
        assertEquals("UGX", capturedReq.currency());
        assertEquals("*************", capturedReq.debit().accountNumber());
        assertEquals("UMEME", capturedValidateRefNumberReq.billerId());
        assertEquals("*************", capturedValidateRefNumberReq.customerReference());
    }
}
