package com.sbg.ug.optimus_backend.exceptions.handlers;

import static org.junit.jupiter.api.Assertions.*;

import com.sbg.ug.optimus_backend.common.Constants;
import com.sbg.ug.optimus_backend.exceptions.BusinessException;
import com.sbg.ug.optimus_backend.exceptions.ErrorCode;
import com.sbg.ug.optimus_backend.exceptions.ServiceException;
import com.sbg.ug.optimus_backend.exceptions.handlers.ExceptionHandler.ErrorResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

class ExceptionHandlerTest {

    private ExceptionHandler exceptionHandler;

    @BeforeEach
    void setUp() {
        exceptionHandler = new ExceptionHandler();
    }

    @Test
    void handleException_ShouldReturnInternalServerError() {
        // Arrange
        Exception exception = new Exception("Test exception");

        // Act
        ResponseEntity<ErrorResponse> responseEntity = exceptionHandler.handleException(exception);

        // Assert
        assertNotNull(responseEntity);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, responseEntity.getStatusCode());

        ErrorResponse errorResponse = responseEntity.getBody();
        assertNotNull(errorResponse);
        assertEquals(ErrorCode.General.INTERNAL_SERVER_ERROR, errorResponse.code());
        assertEquals(Constants.ErrorMessages.GenericError, errorResponse.message());
    }

    @Test
    void handleBusinessException_ShouldReturnBadRequest() {
        // Arrange
        BusinessException businessException =
                new BusinessException(ErrorCode.Billing.BILLER_NOT_FOUND, "Biller not found");

        // Act
        ResponseEntity<ErrorResponse> responseEntity =
                exceptionHandler.handleBusinessException(businessException);

        // Assert
        assertNotNull(responseEntity);
        assertEquals(HttpStatus.BAD_REQUEST.value(), responseEntity.getStatusCodeValue());

        ErrorResponse errorResponse = responseEntity.getBody();
        assertNotNull(errorResponse);
        assertEquals(ErrorCode.Billing.BILLER_NOT_FOUND, errorResponse.code());
        assertEquals("Biller not found", errorResponse.message());
    }

    @Test
    void handleBusinessException_WithDifferentErrorCode() {
        // Arrange
        BusinessException businessException =
                new BusinessException(ErrorCode.Billing.MISSING_FIELD, "Required field is missing");

        // Act
        ResponseEntity<ErrorResponse> responseEntity =
                exceptionHandler.handleBusinessException(businessException);

        // Assert
        assertNotNull(responseEntity);
        assertEquals(HttpStatus.BAD_REQUEST.value(), responseEntity.getStatusCodeValue());

        ErrorResponse errorResponse = responseEntity.getBody();
        assertNotNull(errorResponse);
        assertEquals(ErrorCode.Billing.MISSING_FIELD, errorResponse.code());
        assertEquals("Required field is missing", errorResponse.message());
    }

    @Test
    void errorResponse_ShouldHaveProperGetters() {
        // Arrange & Act
        ErrorResponse errorResponse =
                new ErrorResponse(ErrorCode.Billing.UNSUPPORTED_FIELD, "Field not supported");

        // Assert
        assertEquals(ErrorCode.Billing.UNSUPPORTED_FIELD, errorResponse.code());
        assertEquals("Field not supported", errorResponse.message());
    }

    @Test
    void handleServiceException_ShouldReturnCustomStatusAndErrorResponse() {
        // Arrange
        ServiceException serviceException =
                new ServiceException(
                        ErrorCode.General.INTERNAL_SERVER_ERROR, "Service unavailable", 500);

        // Act
        ResponseEntity<ExceptionHandler.ErrorResponse> responseEntity =
                exceptionHandler.handleServiceException(serviceException);

        // Assert
        assertNotNull(responseEntity);

        ExceptionHandler.ErrorResponse errorResponse = responseEntity.getBody();
        assertNotNull(errorResponse);
        assertEquals(ErrorCode.General.INTERNAL_SERVER_ERROR, errorResponse.code());
        assertEquals("Service unavailable", errorResponse.message());
    }
}
