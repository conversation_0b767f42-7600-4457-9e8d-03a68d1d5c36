package com.sbg.ug.optimus_backend.exceptions;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class ServiceExceptionTest {

    enum DummyErrorCode implements ErrorCode {
        TEST_ERROR
    }

    @Test
    void testConstructorAndGetters() {
        ServiceException ex = new ServiceException(DummyErrorCode.TEST_ERROR, "Test message", 400);
        assertThat(ex.getCode()).isEqualTo(DummyErrorCode.TEST_ERROR);
        assertThat(ex.getMessage()).isEqualTo("Test message");
        assertThat(ex.getStatusCode()).isEqualTo(400);
    }

    @Test
    void testConstructorWithCause() {
        Throwable cause = new RuntimeException("Root cause");
        ServiceException ex =
                new ServiceException(DummyErrorCode.TEST_ERROR, "Test message", cause, 500);
        assertThat(ex.getCode()).isEqualTo(DummyErrorCode.TEST_ERROR);
        assertThat(ex.getMessage()).isEqualTo("Test message");
        assertThat(ex.getCause()).isEqualTo(cause);
        assertThat(ex.getStatusCode()).isEqualTo(500);
    }

    @Test
    void testConstructorWithoutStatus() {
        Throwable cause = new RuntimeException("Root cause");
        ServiceException ex =
                new ServiceException(DummyErrorCode.TEST_ERROR, "Test message", cause);
        assertThat(ex.getCode()).isEqualTo(DummyErrorCode.TEST_ERROR);
        assertThat(ex.getMessage()).isEqualTo("Test message");
        assertThat(ex.getCause()).isEqualTo(cause);
        assertThat(ex.getStatusCode()).isEqualTo(500);
    }
}
