package com.sbg.ug.optimus_backend.finacle.usecase;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import com.sbg.ug.optimus_backend.finacle.external.http.ApicPaymentClient;
import com.sbg.ug.optimus_backend.finacle.external.http.data.PayToAccountDto;
import com.sbg.ug.optimus_backend.finacle.external.http.data.PayToManyAccountsDto;
import com.sbg.ug.optimus_backend.finacle.usecase.data.PayToAccountCmd;
import com.sbg.ug.optimus_backend.finacle.usecase.data.PayToManyAccountsCmd;
import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PayToAccountUseCaseTest {

    @Mock ApicPaymentClient apicPaymentClient;
    @InjectMocks PayToAccountUseCase payToAccountUseCase;

    @Test
    void payToAccount() throws Exception {

        var apicResp = Instancio.create(PayToAccountDto.Resp.class);
        var req = Instancio.create(PayToAccountCmd.Req.class);

        // Mocking the response from the external client
        when(apicPaymentClient.payToAccount(Mockito.any(PayToAccountDto.Req.class)))
                .thenReturn(apicResp);

        var resp = payToAccountUseCase.payToAccount(req);

        // Assertions
        assertNotNull(resp);
        assertThat(resp.description()).isEqualTo(apicResp.description());
        assertThat(resp.statusCode()).isEqualTo(apicResp.statusCode());
    }

    @Test
    void payToManyAccounts() throws Exception {

        var apicResp = Instancio.create(PayToManyAccountsDto.Resp.class);
        var req = Instancio.create(PayToManyAccountsCmd.Req.class);

        // Mocking the response from the external client
        when(apicPaymentClient.payToManyAccounts(Mockito.any(PayToManyAccountsDto.Req.class)))
                .thenReturn(apicResp);

        var resp = payToAccountUseCase.payToManyAccounts(req);
        // Assertions
        assertNotNull(resp);
        assertThat(resp.description()).isEqualTo(apicResp.description());
        assertThat(resp.statusCode()).isEqualTo(apicResp.statusCode());
    }
}
