package com.sbg.ug.optimus_backend.finacle.spi;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import com.sbg.ug.optimus_backend.finacle.usecase.PayToAccountUseCase;
import com.sbg.ug.optimus_backend.finacle.usecase.data.PayToAccountCmd;
import com.sbg.ug.optimus_backend.finacle.usecase.data.PayToManyAccountsCmd;
import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FinaclePaymentSpiTest {

    @Mock PayToAccountUseCase payToAccountUseCase;
    @InjectMocks FinaclePaymentSpi finaclePaymentSpi;

    @Test
    void payToAccount() throws Exception {
        // Given
        var req = Instancio.create(PayToAccountCmd.Req.class);
        var resp = Instancio.create(PayToAccountCmd.Resp.class);

        when(payToAccountUseCase.payToAccount(req)).thenReturn(resp);

        // When
        var result = finaclePaymentSpi.payToAccount(req);

        // Then
        assertEquals(resp, result);
    }

    @Test
    void payToManyAccounts() throws Exception {
        // Given
        var req = Instancio.create(PayToManyAccountsCmd.Req.class);
        var resp = Instancio.create(PayToManyAccountsCmd.Resp.class);

        when(payToAccountUseCase.payToManyAccounts(req)).thenReturn(resp);

        // When
        var result = finaclePaymentSpi.payToManyAccounts(req);

        // Then
        assertEquals(resp, result);
    }
}
