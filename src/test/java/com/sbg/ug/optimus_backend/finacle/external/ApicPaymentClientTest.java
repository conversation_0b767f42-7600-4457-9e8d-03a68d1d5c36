package com.sbg.ug.optimus_backend.finacle.external;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.sbg.ug.optimus_backend.apic.spi.ApicAuthSpi;
import com.sbg.ug.optimus_backend.common.Constants;
import com.sbg.ug.optimus_backend.common.CryptoUtil;
import com.sbg.ug.optimus_backend.common.UnirestLogger;
import com.sbg.ug.optimus_backend.exceptions.ServiceException;
import com.sbg.ug.optimus_backend.finacle.external.http.ApicPaymentClient;
import com.sbg.ug.optimus_backend.finacle.external.http.data.PayToAccountDto;
import com.sbg.ug.optimus_backend.finacle.external.http.data.PayToManyAccountsDto;
import com.sbg.ug.optimus_backend.testutil.DevJsonUtil;
import com.sbg.ug.optimus_backend.testutil.DevUtil;
import java.security.PrivateKey;
import java.util.List;
import kong.unirest.core.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@Slf4j
@ExtendWith(MockitoExtension.class)
class ApicPaymentClientTest {

    static final String APIC_URI = "http://api-connect-uri";
    static final PrivateKey privateKey = DevUtil.generatePrivateKey();
    static final String API_KEY = "api-key";
    static final String API_SECRET = "api-secret";
    static final String X_CHANNEL_ID = "CIMS";

    UnirestInstance unirest;
    MockClient mockClient;
    @InjectMocks ApicPaymentClient apicPaymentClient;
    @Mock ApicAuthSpi apicAuthSpi;

    public static final String APPLICATION_JSON_CHARSET_UTF_8 = "application/json; charset=UTF-8";

    @BeforeEach
    void setUp() {

        unirest = Unirest.spawnInstance();
        unirest.config().defaultBaseUrl(APIC_URI).interceptor(new UnirestLogger());
        mockClient = MockClient.register(unirest);

        ReflectionTestUtils.setField(apicPaymentClient, "unirest", unirest);
        ReflectionTestUtils.setField(apicPaymentClient, "clientKey", API_KEY);
        ReflectionTestUtils.setField(apicPaymentClient, "clientSecret", API_SECRET);
        ReflectionTestUtils.setField(apicPaymentClient, "privateKey", privateKey);
        ReflectionTestUtils.setField(apicPaymentClient, "xChannelId", X_CHANNEL_ID);

        Mockito.when(apicAuthSpi.getToken()).thenReturn("token-111");
    }

    private static String bearerToken() {
        return "Bearer token-111";
    }

    private static String fullUri(String path) {
        return APIC_URI + path;
    }

    Expectation mockCall(HttpMethod method, String uri) {
        return mockClient
                .expect(method, fullUri(uri))
                .header("Accept", APPLICATION_JSON_CHARSET_UTF_8)
                .header("Authorization", bearerToken())
                .header("X-IBM-Client-Id", API_KEY)
                .header("x-channel-id", "CIMS")
                .header("Content-Type", APPLICATION_JSON_CHARSET_UTF_8);
    }

    @Test
    void shouldPayToAccountSuccessFully() throws Exception {

        var payload =
                """
{"messageId":"20250526181841yzbzM9","currency":"UGX","amount":"10000","timestamp":"2025-05-26T18:18:41.533Z","debit":{"accountNumber":"*************","narration":"********** GOTV 202505261818418XRFWG"},"credit":[{"accountNumber":"*************","narration":"********** GOTV 202505261818418XRFWG","amount":"10000"}]}\
""";
        var paymentResp =
                "{\"transactionId\":\"S55324223\",\"requestId\":\"20250526181841yzbzM9\",\"responseDateTime\":\"2025-05-26T15:17:34.976\",\"description\":\"Successful\",\"statusCode\":\"200\"}";
        mockCall(HttpMethod.POST, "/agb-funds-transfer/v1.0.0/inhouse-transfer")
                .body(DevJsonUtil.jsonMatch(payload))
                .header("x-signature", CryptoUtil.sign(privateKey, payload))
                .thenReturn(paymentResp);

        var response =
                apicPaymentClient.payToAccount(
                        new PayToAccountDto.Req(
                                "20250526181841yzbzM9",
                                "UGX",
                                "10000",
                                "2025-05-26T18:18:41.533Z",
                                new PayToAccountDto.Debit(
                                        "*************", "********** GOTV 202505261818418XRFWG"),
                                List.of(
                                        new PayToAccountDto.Credit(
                                                "*************",
                                                "********** GOTV 202505261818418XRFWG",
                                                "10000"))));
        assertThat(response).isNotNull();
        assertThat(response.statusCode()).isEqualTo("200");
        assertThat(response.description()).isEqualTo("Successful");
        assertThat(response.requestId()).isEqualTo("20250526181841yzbzM9");
        assertThat(response.responseDateTime()).isEqualTo("2025-05-26T15:17:34.976");
        assertThat(response.transactionId()).isEqualTo("S55324223");
    }

    @Test
    void shouldPayToManyAccountSuccessFully() throws Exception {

        var payload =
                """
{"messageId":"20250527131733zJTMag","currency":"UGX","amount":"1150.0","timestamp":"2025-05-27T13:17:33.825Z","debit":{"accountNumber":"*************","narration":"Pay GOTV Smart App:**********"},"credit":[{"accountNumber":"031100UGX8024001","narration":"Pay GOTV Smart App:**********","amount":"1000.0"},{"accountNumber":"031100UGX5215050","narration":"Pay GOTV Smart App:**********","amount":"150"}]}\
""";
        var paymentResp =
                "{\"transactionId\":\"S55368884\",\"requestId\":\"20250527131733zJTMag\",\"responseDateTime\":\"2025-05-26T15:17:34.976\",\"description\":\"Successful\",\"statusCode\":\"200\"}";
        mockCall(HttpMethod.POST, "/funds-transfer/batch")
                .body(DevJsonUtil.jsonMatch(payload))
                .header("x-signature", CryptoUtil.sign(privateKey, payload))
                .thenReturn(paymentResp);
        var response =
                apicPaymentClient.payToManyAccounts(
                        new PayToManyAccountsDto.Req(
                                "20250527131733zJTMag",
                                "UGX",
                                "1150.0",
                                "2025-05-27T13:17:33.825Z",
                                new PayToManyAccountsDto.Debit(
                                        "*************", "Pay GOTV Smart App:**********"),
                                List.of(
                                        new PayToManyAccountsDto.Credit(
                                                "031100UGX8024001",
                                                "Pay GOTV Smart App:**********",
                                                "1000.0"),
                                        new PayToManyAccountsDto.Credit(
                                                "031100UGX5215050",
                                                "Pay GOTV Smart App:**********",
                                                "150"))));

        assertThat(response).isNotNull();
        assertThat(response.statusCode()).isEqualTo("200");
        assertThat(response.description()).isEqualTo("Successful");
        assertThat(response.requestId()).isEqualTo("20250527131733zJTMag");
        assertThat(response.responseDateTime()).isEqualTo("2025-05-26T15:17:34.976");
        assertThat(response.transactionId()).isEqualTo("S55368884");
    }

    @Test
    void shouldThrowExceptionWhenPostingToManyAccountsIs500() throws Exception {
        // Arrange
        var payload =
                """
{"messageId":"20250527131733zJTMag","currency":"UGX","amount":"1150.0","timestamp":"2025-05-27T13:17:33.825Z","debit":{"accountNumber":"*************","narration":"Pay GOTV Smart App:**********"},"credit":[{"accountNumber":"031100UGX8024001","narration":"Pay GOTV Smart App:**********","amount":"1000.0"},{"accountNumber":"031100UGX5215050","narration":"Pay GOTV Smart App:**********","amount":"150"}]}\
""";

        mockCall(HttpMethod.POST, "/funds-transfer/batch")
                .body(DevJsonUtil.jsonMatch(payload))
                .header("x-signature", CryptoUtil.sign(privateKey, payload))
                .thenReturn("{\"moreInformation\":\"Technical error, please try again later\"}")
                .withStatus(500);

        ServiceException ex =
                assertThrows(
                        ServiceException.class,
                        () ->
                                apicPaymentClient.payToManyAccounts(
                                        new PayToManyAccountsDto.Req(
                                                "20250527131733zJTMag",
                                                "UGX",
                                                "1150.0",
                                                "2025-05-27T13:17:33.825Z",
                                                new PayToManyAccountsDto.Debit(
                                                        "*************",
                                                        "Pay GOTV Smart App:**********"),
                                                List.of(
                                                        new PayToManyAccountsDto.Credit(
                                                                "031100UGX8024001",
                                                                "Pay GOTV Smart App:**********",
                                                                "1000.0"),
                                                        new PayToManyAccountsDto.Credit(
                                                                "031100UGX5215050",
                                                                "Pay GOTV Smart App:**********",
                                                                "150")))));

        assertThat(ex.getStatusCode()).isEqualTo(500);
        assertThat(ex.getMessage()).isEqualTo(Constants.ErrorMessages.PaymentFailed);
    }

    // Write test if posting to finacle throws an exception
    @Test
    void shouldThrowExceptionWhenPostingToFinacleHttpCodeIs500() throws Exception {
        // Arrange
        var payload =
                """
{"messageId":"20250526181841yzbzM9","currency":"UGX","amount":"10000","timestamp":"2025-05-26T18:18:41.533Z","debit":{"accountNumber":"*************","narration":"********** GOTV 202505261818418XRFWG"},"credit":[{"accountNumber":"*************","narration":"********** GOTV 202505261818418XRFWG","amount":"10000"}]}\
""";

        mockCall(HttpMethod.POST, "/agb-funds-transfer/v1.0.0/inhouse-transfer")
                .body(DevJsonUtil.jsonMatch(payload))
                .header("x-signature", CryptoUtil.sign(privateKey, payload))
                .thenReturn("{\"moreInformation\":\"Technical error, please try again later\"}")
                .withStatus(500);

        ServiceException ex =
                assertThrows(
                        ServiceException.class,
                        () ->
                                apicPaymentClient.payToAccount(
                                        new PayToAccountDto.Req(
                                                "20250526181841yzbzM9",
                                                "UGX",
                                                "10000",
                                                "2025-05-26T18:18:41.533Z",
                                                new PayToAccountDto.Debit(
                                                        "*************",
                                                        "********** GOTV 202505261818418XRFWG"),
                                                List.of(
                                                        new PayToAccountDto.Credit(
                                                                "*************",
                                                                "********** GOTV"
                                                                        + " 202505261818418XRFWG",
                                                                "10000")))));

        assertThat(ex.getStatusCode()).isEqualTo(500);
        assertThat(ex.getMessage()).isEqualTo(Constants.ErrorMessages.PaymentFailed);
    }
}
