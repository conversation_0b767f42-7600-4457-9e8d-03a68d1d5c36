package com.sbg.ug.optimus_backend;

import static com.sbg.ug.optimus_backend.billing.domain.BillerBuilder.biller;
import static com.sbg.ug.optimus_backend.billing.domain.BillerBuilder.field;
import static com.sbg.ug.optimus_backend.billing.domain.BillerBuilder.flow;
import static com.sbg.ug.optimus_backend.billing.domain.BillerBuilder.step;

import com.sbg.ug.optimus_backend.billing.controller.v1.BillerController;
import com.sbg.ug.optimus_backend.billing.domain.Billers;
import com.sbg.ug.optimus_backend.billing.usecase.*;
import com.sbg.ug.optimus_backend.billing.usecase.data.*;
import com.sbg.ug.optimus_backend.common.JsonUtil;
import com.sbg.ug.optimus_backend.otp.controller.v1.SmsController;
import com.sbg.ug.optimus_backend.otp.usecase.GenerateOtpUseCase;
import com.sbg.ug.optimus_backend.otp.usecase.data.GenerateSmsOtpCmd;
import com.sbg.ug.optimus_backend.ping.controller.v1.PingController;
import com.sbg.ug.optimus_backend.spikes.BillerSpikeController;
import io.restassured.module.mockmvc.RestAssuredMockMvc;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

@SuppressWarnings("PMD.TestClassWithoutTestCases")
public class BaseContract {

    @SneakyThrows
    private static BillerController setUpBillerController() {

        // We don't need to instantiate the mappers directly as they are interfaces
        // MapStruct will create implementations at compile time
        // The Mapper class is a utility class with static fields

        Billers.Biller biller =
                biller(
                        "billerId",
                        "billerName",
                        "Some Category",
                        flow(
                                "flowId",
                                "flowName",
                                step(
                                        "stepId",
                                        "stepName",
                                        field("fieldId", "fieldName", "fieldType"))));

        Billers billers = new Billers(List.of(biller));

        ListBillersUseCase listBillersUseCase = Mockito.mock();

        Mockito.when(listBillersUseCase.listBillers()).thenReturn(billers);

        // Add mock for listByCategory to return the same billers for any category id
        Mockito.when(listBillersUseCase.listByCategory("pay-tv")).thenReturn(billers);

        // Add mock for getBiller to return the biller for id "biller1"
        Mockito.when(listBillersUseCase.getBiller("biller1")).thenReturn(Optional.of(biller));

        ValidateCustomerRefUseCase validateCustomerRefUseCase = Mockito.mock();

        List<ValidateRefNumberCmd.DisplayAmount> charges = new ArrayList<>();
        charges.add(new ValidateRefNumberCmd.DisplayAmount("Charges", BigDecimal.TEN));

        Mockito.when(validateCustomerRefUseCase.validate(Mockito.any()))
                .thenReturn(
                        new ValidateRefNumberCmd.Resp(
                                BigDecimal.TEN,
                                "description",
                                "UGX",
                                "equalorgreater",
                                false,
                                "241811621",
                                BigDecimal.ZERO,
                                charges));

        GetPackagesUseCase getPackagesUseCase = Mockito.mock();
        Mockito.when(getPackagesUseCase.pullBillerPackages(Mockito.any()))
                .thenReturn(
                        new GetPackagesCmd.Resp(
                                "title",
                                List.of(
                                        new GetPackagesCmd.Package(
                                                "packageId",
                                                "description",
                                                "price",
                                                "UGX",
                                                "title"))));

        ListCategoriesUseCase listCategoriesUseCase = Mockito.mock();
        Mockito.when(listCategoriesUseCase.listCategories())
                .thenReturn(
                        new ListCategoriesCmd.Resp(
                                List.of(
                                        new ListCategoriesCmd.Category(
                                                "categoryId", "categoryName", "categoryIcon"))));

        PaymentReviewUseCase paymentReviewUseCase = Mockito.mock();
        Mockito.when(paymentReviewUseCase.generatePaymentId())
                .thenReturn((new PaymentReviewCmd.Resp("9f14bcf2-d0f2-42d2-a71b-79e1b019865b")));

        BillerController billerController = new BillerController();

        ReflectionTestUtils.setField(billerController, "listBillersUseCase", listBillersUseCase);
        ReflectionTestUtils.setField(
                billerController, "validateCustomerRefUseCase", validateCustomerRefUseCase);
        ReflectionTestUtils.setField(billerController, "getPackagesUseCase", getPackagesUseCase);
        ReflectionTestUtils.setField(
                billerController, "listCategoriesUseCase", listCategoriesUseCase);
        ReflectionTestUtils.setField(
                billerController, "paymentReviewUseCase", paymentReviewUseCase);
        ReflectionTestUtils.setField(billerController, "versionNumber", "1");
        return billerController;
    }

    @BeforeEach
    public void setUp() throws Exception {
        BillerController billerController = setUpBillerController();
        NotifyPaymentUseCase notifyPaymentUseCase = Mockito.mock();

        BillerSpikeController billerSpikeController = new BillerSpikeController();
        ReflectionTestUtils.setField(
                billerSpikeController, "notifyPaymentUseCase", notifyPaymentUseCase);

        Mockito.when(notifyPaymentUseCase.notifyPayment(Mockito.any()))
                .thenReturn(
                        new NotifyPaymentCmd.Resp(
                                "S55368884",
                                "20250527131730OalGSy",
                                "2025-05-27T10:16:23.911",
                                "Successful",
                                "200"));

        // Register OTP controller
        SmsController smsController = new SmsController();
        GenerateOtpUseCase generateOtpUseCase = Mockito.mock();
        ReflectionTestUtils.setField(smsController, "generateOtpUseCase", generateOtpUseCase);
        var response =
                "{\"requestId\":\"****************\",\"accountId\":\"*************\",\"accountName\":\"John"
                    + " Doe\",\"transactionType\":\"LMTR\",\"mobile\":\"************\","
                    + "\"statusCode\":\"0\",\"statusDescription\":\"Successful\"}";
        Mockito.when(generateOtpUseCase.generateSmsOtp(Mockito.any()))
                .thenReturn(JsonUtil.toClassObject(response, GenerateSmsOtpCmd.Resp.class));

        RestAssuredMockMvc.standaloneSetup(
                new PingController(), billerController, billerSpikeController, smsController);
    }
}
