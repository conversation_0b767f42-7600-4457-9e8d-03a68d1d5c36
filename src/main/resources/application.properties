spring.application.name=optimus-backend
api.connect.base_url=https://apis-ppd-ug-gw-gateway-cp4i.apps.sgb-ug-rosa-np.0n8w.p1.openshiftapps.com/pp-providerorg1/internal-uat-ug
#api.connect.client_secret=none
#api.connect.client_key=none
#api.connect.signing_key=none

api.connect.x_channel_id=OPTI

# Database Configuration
spring.datasource.url=*******************************************
spring.datasource.username=postgres
spring.datasource.password=password123
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Liquibase Configuration
spring.liquibase.change-log=classpath:db/changelog/db.changelog-master.xml
spring.liquibase.enabled=true

#logging.structured.format.console=ecs
#logging.structured.ecs.service.environment=sit

# add logging for unirest
logging.level.org.apache.http=trace
logging.level.kong.unirest.core=trace
logging.pattern.console=[%10.10thread] %clr(%d{yyyy-MM-dd HH:mm:ss}){faint} %clr(%5p) %clr(:){faint} %m %clr(%mdc){cyan}%n
management.endpoints.web.exposure.include=prometheus,health,info,httpexchanges

#Authentication
spring.security.oauth2.resourceserver.jwt.issuer-uri=https://enterprisestssit.standardbank.co.za
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=https://enterprisestssit.standardbank.co.za/ext/sbg/customer/oauth/jwks

#Version
version.number=v1.0.0