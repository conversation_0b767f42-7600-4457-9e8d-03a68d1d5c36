<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <changeSet id="001" author="optimus">
        <!-- Example table creation -->
        <createTable tableName="example">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="text"/>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <!-- Add more schema changes as needed -->
    </changeSet>

    <changeSet id="*************-1" author="kayondoronald (generated)">
        <dropColumn tableName="example" columnName="description"/>
    </changeSet>

    <changeSet id="*************-2" author="optimus">
        <createTable tableName="biller_configs">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="config_name" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="config" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="status" defaultValue="0" type="int">
                <constraints  nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="*************-3" author="optimus">
        <createIndex indexName="idx_config_name" tableName="biller_configs">
            <column name="config_name" />
        </createIndex>
    </changeSet>
    <changeSet id="*************-4" author="optimus">
        <insert tableName="biller_configs">
            <column name="config_name">
                NARRATIONS
            </column>
            <column name="config"><![CDATA[
             { "ENCOT": { "DebitNarration": "${ChannelTxnID} AGB CASHDEP", "CreditNarration": "${CustomerName} ${ChannelTxnID}", "BillerNarration": "ENCOT Payment" }, "STARTIMESTV": { "DebitNarration": "${ChannelTxnID} AGB CASHDEP", "CreditNarration": "AGENT BANKING ${ChannelTxnID}", "BillerNarration": "STARTIMESTV Payment" }, "TOTAL": { "DebitNarration": "${CustomerRefNum} TOTAL ${ChannelTxnID}", "CreditNarration": "${CustomerRefNum} TOTAL ${ChannelTxnID}", "BillerNarration": "TOTAL Payment" }, "WAVE": { "DebitNarration": "${ChannelTxnID} ${CustomerRefNum} WAVE", "CreditNarration": "${CustomerRefNum} ${ChannelTxnID} WAVE", "BillerNarration": "WAVE Payment" }, "BODABANJA": { "DebitNarration": "${ChannelTxnID} AGB CASHDEP", "CreditNarration": "${CustomerRefNum} ${CustomerName} ${ChannelTxnID}", "BillerNarration": "BODABANJA Payment" }, "BODABANJA-C": { "DebitNarration": "${ChannelTxnID} AGB CASHDEP", "CreditNarration": "${CustomerRefNum} ${CustomerName} ${ChannelTxnID}", "BillerNarration": "BODABANJA Payment" }, "UEDCL": { "DebitNarration": "${ChannelTxnID} ${CustomerRefNum} UEDCL ", "CreditNarration": "${CustomerRefNum} ${ChannelTxnID} UEDCL", "BillerNarration": "UEDCL Payment" }, "ZUKUTV": { "DebitNarration": "${ChannelTxnID} AGB CASHDEP", "CreditNarration": "AGENCY BANKING ${ChannelTxnID}", "BillerNarration": "ZUKUTV Payment" }, "AZAMTV": { "DebitNarration": "${ChannelTxnID} AGB CASHDEP", "CreditNarration": "AGENT BANKING ${ChannelTxnID}", "BillerNarration": "AZAMTV Payment" }, "MOGOLOANS": { "DebitNarration": "${ChannelTxnID} ${CustomerRefNum} MOGOLOAN", "CreditNarration": "${CustomerRefNum} ${ChannelTxnID} MOGOLOAN", "BillerNarration": "MOGOLOANS Payment" }, "ERA": { "DebitNarration": "${ChannelTxnID} AGB CASHDEP", "CreditNarration": "${CustomerName} ${ChannelTxnID}", "BillerNarration": "ERA Payment" }, "IOTEC": { "DebitNarration": "${ChannelTxnID} AGB CASHDEP", "CreditNarration": "${ChannelTxnID} ${DepositorPhoneNumber}", "BillerNarration": "IOTEC Payment" }, "DSTV": { "DebitNarration": "${CustomerRefNum} DSTV ${ChannelTxnID}", "CreditNarration": "${CustomerRefNum} DSTV ${ChannelTxnID}", "BillerNarration": "DSTV Payment" }, "NWSC": { "DebitNarration": "${CustomerRefNum} NWSC ${ChannelTxnID}", "CreditNarration": "${CustomerRefNum}-NWSC-${ChannelTxnID}", "BillerNarration": "NWSC Payment" }, "UMEME": { "DebitNarration": "${CustomerRefNum} UMEME ${ChannelTxnID}", "CreditNarration": "${CustomerRefNum} UMEME ${ChannelTxnID}", "BillerNarration": "UMEME Payment" }, "NDA": { "DebitNarration": "${ChannelTxnID} AGB CASHDEP", "CreditNarration": "${CustomerRefNum} NDA ${ChannelTxnID}", "BillerNarration": "NDA Payment" }, "KIS": { "DebitNarration": "${ChannelTxnID} AGB CASHDEP", "CreditNarration": "${CustomerRefNum} ${CustomerName} ${ChannelTxnID}", "BillerNarration": "KIS Payment" }, "NSSFV": { "DebitNarration": "${ChannelTxnID} ${CustomerRefNum}-NSSF-AGB", "CreditNarration": "${ChannelTxnID} ${CustomerRefNum}-NSSF-AGB", "BillerNarration": "NSSF Payment" }, "NSSFM": { "DebitNarration": "${ChannelTxnID} ${CustomerRefNum}-NSSF-AGB", "CreditNarration": "${ChannelTxnID} ${CustomerRefNum}-NSSF-AGB", "BillerNarration": "NSSF Payment" }, "ROKE": { "DebitNarration": "${ChannelTxnID} ${CustomerRefNum} ROKE-AGB", "CreditNarration": "${CustomerRefNum} ${ChannelTxnID} ROKE-AGB", "BillerNarration": "ROKE Payment" }, "CHAPCHAP": { "DebitNarration": "${ChannelTxnID} ${CustomerRefNum} CHAPCHAP", "CreditNarration": "${CustomerRefNum} ${ChannelTxnID} CHAPCHAP", "BillerNarration": "CHAPCHAP Payment" }, "GOTV": { "DebitNarration": "${CustomerRefNum} GOTV ${ChannelTxnID}", "CreditNarration": "${CustomerRefNum} GOTV ${ChannelTxnID}", "BillerNarration": "GOTV Payment" }, "URA": { "DebitNarration": "${CustomerRefNum} URA ${ChannelTxnID}", "CreditNarration": "${CustomerRefNum} URA ${ChannelTxnID}", "BillerNarration": "URA Payment" }, "TORORO": { "DebitNarration": "${ChannelTxnID} AGB CASHDEP", "CreditNarration": "${ChannelTxnID} ${DepositorPhoneNumber}", "BillerNarration": "TORORO CEMENT Payment" }, "JESA": { "DebitNarration": "${ChannelTxnID} AGB CASHDEP", "CreditNarration": "${ChannelTxnID} ${DepositorPhoneNumber}", "BillerNarration": "JESA Payment" }, "TOTALBILLS": { "DebitNarration": "${CustomerRefNum} TOTAL ${ChannelTxnID}", "CreditNarration": "${CustomerRefNum} TOTAL ${ChannelTxnID} ${DepositorPhoneNumber}", "BillerNarration": "TOTAL Bills Payment" }, "SICPA": { "DebitNarration": "${ChannelTxnID} AGB CASHDEP", "CreditNarration": "${CustomerRefNum} AGB SICPA ${ChannelTxnID}", "BillerNarration": "SICPA Payment" }, "UICT": { "DebitNarration": " ${ChannelTxnID} AGB CASHDEP", "CreditNarration": "${CustomerRefNum} ${CustomerName}", "BillerNarration": "UICT Payment" }}
            ]]></column>
            <column name="status">
                1
            </column>
        </insert>
    </changeSet>
    <changeSet id="*************-5" author="optimus">
        <insert tableName="biller_configs">
            <column name="config_name">
                UNSUPPORTEDBILLERS
            </column>
            <column name="config">
                <![CDATA[
                ["HAMILTON", "SICPA", "WAVE", "ONAFRIQ", "CHAPCHAP"]
                ]]>
            </column>
            <column name="status">
                1
            </column>
        </insert>
    </changeSet>
    <changeSet id="*************-6" author="optimus">
        <insert tableName="biller_configs">
            <column name="config_name">
                BILLERCHARGES
            </column>
            <column name="config">
                <![CDATA[
                { "ENCOT": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "STARTIMESTV": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "TOTAL": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "WAVE": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "BODABANJA": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "BODABANJA-C": { "MainTransactionCharge": 0, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "UEDCL": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "ZUKUTV": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "AZAMTV": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "MOGOLOANS": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "ERA": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "IOTEC": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "DSTV": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "NWSC": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "UMEME": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15 }, "NDA": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "KIS": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "NSSFV": { "MainTransactionCharge": 0, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "NSSFM": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "ROKE": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "CHAPCHAP": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "GOTV": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "URA": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "TORORO": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "JESA": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "TOTALBILLS": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "SICPA": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "UICT": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" } }
                ]]>
            </column>
            <column name="status">
                1
            </column>
        </insert>
    </changeSet>
    <changeSet id="*************-7" author="optimus">
            <update tableName="biller_configs">
                <column name="config" type="text">
                    <![CDATA[
                     {"ENCOT": { "DebitNarration": "${ChannelTxnID} AGB CASHDEP", "CreditNarration": "${CustomerName} ${ChannelTxnID}", "BillerNarration": "${CustomerName} ${CustomerRefNum} ${ChannelTxnID} Smart App" }, "STARTIMESTV": { "DebitNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "${packageId} ${CustomerRefNum} ${ChannelTxnID}" }, "TOTAL": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "TOTAL Payment" }, "BODABANJA": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "BODABANJA Payment" }, "BODABANJA-C": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "${CustomerRefNum}, ${CustomerName}, ${ChannelTxnID}" }, "UEDCL": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "UEDCL Payment" }, "ZUKUTV": { "DebitNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "${packageId} ${CustomerRefNum} ${ChannelTxnID}" }, "AZAMTV": { "DebitNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "${packageId} ${CustomerRefNum} ${ChannelTxnID}" }, "MOGOLOANS": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "${CustomerRefNum} ${ChannelTxnID} MOGOLOAN Smart App" }, "ERA": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "ERA Payment" }, "IOTEC": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "IOTEC Payment" }, "DSTV": { "DebitNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}" }, "NWSC": { "DebitNarration": "Pay water ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay water ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "${packageId} ${CustomerRefNum}" }, "UMEME": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "${CustomerRefNum}-UEDCL ${ChannelTxnID}-Smart App" }, "NDA": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "${CustomerRefNum}/${CustomerName} - ${ChannelTxnID} Smart App" }, "KIS": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "KIS Payment" }, "NSSFV": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "${ChannelTxnID} RRN-NSSF-Smart App" }, "NSSFM": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "${ChannelTxnID} RRN-NSSF-Smart App" }, "ROKE": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "${CustomerRefNum} - ${ChannelTxnID} ROKE Smart App" }, "GOTV": { "DebitNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}" }, "URA": { "DebitNarration": "Pay Taxes ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay Taxes ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "Pay Taxes ${billerId} Smart App: ${CustomerRefNum}" }, "TORORO": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "TORORO CEMENT Payment" }, "JESA": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "JESA Payment" }, "TOTALBILLS": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "${CustomerRefNum}-TOTAL-${ChannelTxnID}" }, "UICT": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration":"Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "UICT Payment" }}
                    ]]>
                </column>
                <where>config_name = 'NARRATIONS'</where>
            </update>
    </changeSet>
    <changeSet id="*************-8" author="optimus">
        <update tableName="biller_configs">
            <column name="config" type="text">
                <![CDATA[
                {"ENCOT": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "STARTIMESTV": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "TOTAL": { "MainTransactionCharge": 1000, Pay TV $SERVICE_PROVIDER_INFO$ Smart App: $CONSUMER_CODE$ "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "BODABANJA": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "BODABANJA-C": { "MainTransactionCharge": 0, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "UEDCL": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "ZUKUTV": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "AZAMTV": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "MOGOLOANS": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "ERA": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "IOTEC": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "DSTV": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "NWSC": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "UMEME": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15 }, "NDA": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "KIS": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "NSSFV": { "MainTransactionCharge": 0, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "NSSFM": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "ROKE": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "GOTV": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "URA": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "TORORO": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "JESA": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "TOTALBILLS": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "UICT": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }}
                ]]>
            </column>
            <where>config_name = 'BILLERCHARGES'</where>
        </update>
    </changeSet>
    <changeSet id="*************-9" author="optimus">
        <update tableName="biller_configs">
            <column name="config" type="text">
                <![CDATA[
                { "ENCOT": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "STARTIMESTV": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "TOTAL": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "BODABANJA": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "BODABANJA-C": { "MainTransactionCharge": 0, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "UEDCL": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "ZUKUTV": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "AZAMTV": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "MOGOLOANS": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "ERA": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "IOTEC": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "DSTV": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "NWSC": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "UMEME": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15 }, "NDA": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "KIS": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "NSSFV": { "MainTransactionCharge": 0, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "NSSFM": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "ROKE": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "GOTV": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "URA": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "TORORO": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "JESA": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "TOTALBILLS": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "UICT": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }}
                ]]>
            </column>
            <where>config_name = 'BILLERCHARGES'</where>
        </update>
    </changeSet>
    <changeSet id="*************-10" author="optimus">
        <update tableName="biller_configs">
            <column name="config" type="text">
                <![CDATA[
                { "ENCOT": { "MainTransactionCharge": 1000, "DebitNarration": "ENCOT  Pay Charges", "CreditNarration": "ENCOT  Pay Charges", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "STARTIMESTV": { "MainTransactionCharge": 1000, "DebitNarration": "STARTIMES TV Pay Charges", "CreditNarration": "STARTIMES TV Pay Charges", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "TOTAL": { "MainTransactionCharge": 0, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "BODABANJA": { "MainTransactionCharge": 0, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "BODABANJA-C": { "MainTransactionCharge": 0, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "UEDCL": { "MainTransactionCharge": 1000, "DebitNarration": "UEDCL Pay Charges", "CreditNarration": "UEDCL Pay Charges", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "ZUKUTV": { "MainTransactionCharge": 1000, "DebitNarration": "ZUKU TV Pay Charges", "CreditNarration": "ZUKU TV Pay Charges", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "AZAMTV": { "MainTransactionCharge": 1000, "DebitNarration": "AZAM TV Pay Charges", "CreditNarration": "AZAM TV Pay Charges", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "MOGOLOANS": { "MainTransactionCharge": 1000, "DebitNarration": "MOGOLOAN  Pay Charges", "CreditNarration": "MOGOLOAN  Pay Charges", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "ERA": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "IOTEC": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "DSTV": { "MainTransactionCharge": 1000, "DebitNarration": "DSTV TV Pay Charges", "CreditNarration": "DSTV TV Pay Charges", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "NWSC": { "MainTransactionCharge": 1000, "DebitNarration": "NWSC Pay Charges", "CreditNarration": "NWSC Pay Charges", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "UMEME": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15 }, "NDA": { "MainTransactionCharge": 1000, "DebitNarration": "NDA Pay Charges", "CreditNarration": "NDA Pay Charges", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "KIS": { "MainTransactionCharge": 1000, "DebitNarration": "KIS  Pay Charges", "CreditNarration": "KIS  Pay Charges", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "NSSFV": { "MainTransactionCharge": 0, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "NSSFM": { "MainTransactionCharge": 0, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "ROKE": { "MainTransactionCharge": 0, "DebitNarration": "ROKE Pay Charges", "CreditNarration": "ROKE Pay Charges", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "GOTV": { "MainTransactionCharge": 1000, "DebitNarration": "GOTV TV Pay Charges", "CreditNarration": "GOTV TV Pay Charges", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "URA": { "MainTransactionCharge": 1000, "DebitNarration": "URA Pay Charges", "CreditNarration": "URA Pay Charges", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "TORORO": { "MainTransactionCharge": 1000, "DebitNarration": "TORORO CEMENT Pay Charges", "CreditNarration": "TORORO CEMENT Pay Charges", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "JESA": { "MainTransactionCharge": 1000, "DebitNarration": "JESA FARM DAIRY Pay Charges", "CreditNarration": "JESA FARM DAIRY Pay Charges", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "TOTALBILLS": { "MainTransactionCharge": 0, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "UICT": { "MainTransactionCharge": 1000, "DebitNarration": "UICT Pay Charges", "CreditNarration": "UICT Pay Charges", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }, "MTN_PREPAID_SERVICES":{ "MainTransactionCharge": 1000, "DebitNarration": "MTN Prepaid Charges", "CreditNarration": "MTN Prepaid Pay Charges", "ExciseDuty": 15, "Excise": "0310XXUGX5215051", "Revenue": "0310XXUGX8006004" }}
                ]]>
            </column>
            <where>config_name = 'BILLERCHARGES'</where>
        </update>
    </changeSet>
    <changeSet id="*************-11" author="optimus">
        <update tableName="biller_configs">
            <column name="config" type="text">
                <![CDATA[
                      {"ENCOT": { "DebitNarration": "${ChannelTxnID} ${billerId} ${CustomerRefNum}", "CreditNarration": "${CustomerName} ${ChannelTxnID} Smart App ${CustomerRefNum}", "BillerNarration": "${CustomerName} ${ChannelTxnID} Smart App ${CustomerRefNum}" }, "STARTIMESTV": { "DebitNarration": "${ChannelTxnID} ${billerId} ${CustomerRefNum}", "CreditNarration": "${packageId} ${CustomerRefNum} ${ChannelTxnID}", "BillerNarration": "${packageId} ${CustomerRefNum} ${ChannelTxnID}" }, "TOTAL": { "DebitNarration": "${ChannelTxnID} ${billerId} Smart App ${CustomerRefNum}", "CreditNarration": "${CustomerRefNum} ${ChannelTxnID}", "BillerNarration": "${CustomerRefNum} ${ChannelTxnID}" }, "BODABANJA": { "DebitNarration": "${ChannelTxnID} ${billerId} Smart App ${CustomerRefNum}", "CreditNarration": "${CustomerRefNum} ${CustomerName} ${ChannelTxnID}", "BillerNarration": "${CustomerRefNum} ${CustomerName} ${ChannelTxnID}" }, "BODABANJA-C": { "DebitNarration": "${ChannelTxnID} ${billerId} Smart App ${CustomerRefNum}", "CreditNarration": "${CustomerRefNum} ${CustomerName} ${ChannelTxnID}", "BillerNarration": "${CustomerRefNum} ${CustomerName} ${ChannelTxnID}" }, "UEDCL": { "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "BillerNarration": "UEDCL Payment" }, "ZUKUTV": { "DebitNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "${packageId} ${CustomerRefNum} ${ChannelTxnID}", "BillerNarration": "${packageId} ${CustomerRefNum} ${ChannelTxnID}" }, "AZAMTV": { "DebitNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "${packageId} ${CustomerRefNum} ${ChannelTxnID}", "BillerNarration": "${packageId} ${CustomerRefNum} ${ChannelTxnID}" }, "MOGOLOANS": { "DebitNarration": "${ChannelTxnID} ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "${CustomerRefNum} ${ChannelTxnID} MOGOLOAN", "BillerNarration": "${CustomerRefNum} ${ChannelTxnID} MOGOLOAN" }, "ERA": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "ERA Payment" }, "IOTEC": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "IOTEC Payment" }, "DSTV": { "DebitNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}" }, "NWSC": { "DebitNarration": "Pay water ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "${packageId} ${CustomerRefNum}", "BillerNarration": "${packageId} ${CustomerRefNum}" }, "UMEME": { "DebitNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "${CustomerRefNum}-UEDCL ${ChannelTxnID}-Smart App" }, "NDA": { "DebitNarration": "${ChannelTxnID} ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "${CustomerRefNum} ${ChannelTxnID}", "BillerNarration": "${CustomerRefNum} ${ChannelTxnID}" }, "KIS": { "DebitNarration": "${ChannelTxnID} ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "${CustomerRefNum} ${ChannelTxnID", "BillerNarration": "KIS  Pay Charges" }, "NSSFV": { "DebitNarration": "${ChannelTxnID} ${billerId} Smart App ${CustomerRefNum}", "CreditNarration": "${ChannelTxnID} ${CustomerRefNum}", "BillerNarration": "${ChannelTxnID} ${CustomerRefNum}" }, "NSSFM": { "DebitNarration": "${ChannelTxnID} ${billerId} Smart App ${CustomerRefNum}", "CreditNarration": "${ChannelTxnID} ${CustomerRefNum}", "BillerNarration": "${ChannelTxnID} ${CustomerRefNum}" }, "ROKE": { "DebitNarration": "${ChannelTxnID} ${billerId} Smart App ${CustomerRefNum}", "CreditNarration": "${CustomerRefNum} ${ChannelTxnID}", "BillerNarration": "${CustomerRefNum} ${ChannelTxnID}" }, "GOTV": { "DebitNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "Pay TV ${billerId} Smart App: ${CustomerRefNum}" }, "URA": { "DebitNarration": "Pay Taxes ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay Taxes ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "Pay Taxes ${billerId} Smart App: ${CustomerRefNum}" }, "TORORO": { "DebitNarration": "${ChannelTxnID} ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "${phoneNumber} Smart App", "BillerNarration": "TORORO CEMENT Payment" }, "JESA": { "DebitNarration": "${ChannelTxnID} ${billerId} Smart App: ${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App: ${CustomerRefNum}", "BillerNarration": "JESA Payment" }, "TOTALBILLS": { "DebitNarration": "${ChannelTxnID} ${billerId} Smart App ${CustomerRefNum}", "CreditNarration": "${CustomerRefNum} ${ChannelTxnID}", "BillerNarration": "${CustomerRefNum} ${ChannelTxnID}" }, "UICT": { "DebitNarration": "${ChannelTxnID} ${billerId} Smart App ${CustomerRefNum}", "CreditNarration":"${CustomerRefNum} ${ChannelTxnID}", "BillerNarration": "UICT Payment" }, "MTN_PREPAID_SERVICES": { "DebitNarration": "${ChannelTxnID} ${billerId} Smart App ${CustomerRefNum}", "CreditNarration":"${CustomerRefNum} ${ChannelTxnID}", "BillerNarration": "MTN Prepaid Service Payment" }}
                ]]>
            </column>
            <where>config_name = 'NARRATIONS'</where>
        </update>
    </changeSet>
    <changeSet id="*************-12" author="optimus">
        <update tableName="biller_configs">
            <column name="config" type="text">
                <![CDATA[
                 {"ENCOT": { "MainTransactionCharge": 1000, "DebitNarration": "ENCOT  Pay Charges", "CreditNarration": "ENCOT  Pay Charges", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "STARTIMESTV": { "MainTransactionCharge": 1000, "DebitNarration": "STARTIMES TV Pay Charges", "CreditNarration": "STARTIMES TV Pay Charges", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "TOTAL": { "MainTransactionCharge": 0, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "BODABANJA": { "MainTransactionCharge": 0, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "BODABANJA-C": { "MainTransactionCharge": 0, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "UEDCL": { "MainTransactionCharge": 1000, "DebitNarration": "UEDCL Pay Charges", "CreditNarration": "UEDCL Pay Charges", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "ZUKUTV": { "MainTransactionCharge": 1000, "DebitNarration": "ZUKU TV Pay Charges", "CreditNarration": "ZUKU TV Pay Charges", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "AZAMTV": { "MainTransactionCharge": 1000, "DebitNarration": "AZAM TV Pay Charges", "CreditNarration": "AZAM TV Pay Charges", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "MOGOLOANS": { "MainTransactionCharge": 1000, "DebitNarration": "MOGOLOAN  Pay Charges", "CreditNarration": "MOGOLOAN  Pay Charges", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "ERA": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "IOTEC": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "DSTV": { "MainTransactionCharge": 1000, "DebitNarration": "DSTV TV Pay Charges", "CreditNarration": "DSTV TV Pay Charges", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "NWSC": { "MainTransactionCharge": 1000, "DebitNarration": "NWSC Pay Charges", "CreditNarration": "NWSC Pay Charges", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "UMEME": { "MainTransactionCharge": 1000, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15 }, "NDA": { "MainTransactionCharge": 1000, "DebitNarration": "NDA Pay Charges", "CreditNarration": "NDA Pay Charges", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "KIS": { "MainTransactionCharge": 1000, "DebitNarration": "KIS  Pay Charges", "CreditNarration": "KIS  Pay Charges", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "NSSFV": { "MainTransactionCharge": 0, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "NSSFM": { "MainTransactionCharge": 0, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "ROKE": { "MainTransactionCharge": 0, "DebitNarration": "ROKE Pay Charges", "CreditNarration": "ROKE Pay Charges", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "GOTV": { "MainTransactionCharge": 1000, "DebitNarration": "GOTV TV Pay Charges", "CreditNarration": "GOTV TV Pay Charges", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "URA": { "MainTransactionCharge": 1000, "DebitNarration": "URA Pay Charges", "CreditNarration": "URA Pay Charges", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "TORORO": { "MainTransactionCharge": 1000, "DebitNarration": "TORORO CEMENT Pay Charges", "CreditNarration": "TORORO CEMENT Pay Charges", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "JESA": { "MainTransactionCharge": 1000, "DebitNarration": "JESA FARM DAIRY Pay Charges", "CreditNarration": "JESA FARM DAIRY Pay Charges", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "TOTALBILLS": { "MainTransactionCharge": 0, "DebitNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "CreditNarration": "Pay ${billerId} Smart App:${CustomerRefNum}", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "UICT": { "MainTransactionCharge": 1000, "DebitNarration": "UICT Pay Charges", "CreditNarration": "UICT Pay Charges", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }, "MTN_PREPAID_SERVICES":{ "MainTransactionCharge": 1000, "DebitNarration": "MTN Prepaid Charges", "CreditNarration": "MTN Prepaid Pay Charges", "ExciseDuty": 15, "Excise": "031100UGX5215050", "Revenue": "031100UGX8024001" }}
                ]]>
            </column>
            <where>config_name = 'BILLERCHARGES'</where>
        </update>
    </changeSet>

</databaseChangeLog>



