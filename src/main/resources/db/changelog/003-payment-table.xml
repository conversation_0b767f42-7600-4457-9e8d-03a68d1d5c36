<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <changeSet id="20250525" author="annacarr">
        <createTable tableName="payments">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="payment_id" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="status_code" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="request_id" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="response_date_time" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="transaction_id" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="resp_remarks" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="http_message" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="20250617" author="annacarr">
        <dropColumn tableName="payments" columnName="http_message"/>
        <dropColumn tableName="payments" columnName="resp_remarks"/>
    </changeSet>
</databaseChangeLog>