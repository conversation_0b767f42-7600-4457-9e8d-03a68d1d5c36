package com.sbg.ug.optimus_backend.billing.orchastration;

public record RetryPolicy(
        int maxAttempts, long initialDelay, double backoffMultiplier, long maxDelay) {

    public static RetryPolicy defaultPolicy() {
        return new RetryPolicy(3, 1000, 2.0, 10000);
    }

    @Override
    public String toString() {
        return "RetryPolicy{"
                + "maxAttempts="
                + maxAttempts
                + ", initialDelay="
                + initialDelay
                + ", backoffMultiplier="
                + backoffMultiplier
                + ", maxDelay="
                + maxDelay
                + '}';
    }
}
