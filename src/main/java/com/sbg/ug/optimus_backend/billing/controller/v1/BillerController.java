package com.sbg.ug.optimus_backend.billing.controller.v1;

import com.sbg.ug.optimus_backend.billing.controller.v1.data.*;
import com.sbg.ug.optimus_backend.billing.controller.v1.mappers.Mapper;
import com.sbg.ug.optimus_backend.billing.usecase.*;
import com.sbg.ug.optimus_backend.billing.usecase.data.GetPackagesCmd;
import com.sbg.ug.optimus_backend.billing.usecase.data.ValidateRefNumberCmd;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/billers")
@Slf4j
public class BillerController {

    @Value("${version.number}")
    private String versionNumber;

    @Autowired ListBillersUseCase listBillersUseCase;
    @Autowired ValidateCustomerRefUseCase validateCustomerRefUseCase;
    @Autowired GetPackagesUseCase getPackagesUseCase;
    @Autowired ListCategoriesUseCase listCategoriesUseCase;
    @Autowired PaymentReviewUseCase paymentReviewUseCase;

    @GetMapping
    public Resp listBillers() throws Exception {
        return Mapper.BILLER_MAPPER.toBillerResp(listBillersUseCase.listBillers());
    }

    @GetMapping("/categories")
    public List<ListCategoriesRequest.Category> listCategories() throws Exception {
        return Mapper.BILLER_MAPPER.toCategoryResp(listCategoriesUseCase.listCategories());
    }

    @GetMapping("/categories/{id}/billers")
    public List<Biller> listBillersByCategory(@PathVariable("id") String id) throws Exception {
        return Mapper.BILLER_MAPPER.toBillerListSummary(listBillersUseCase.listByCategory(id));
    }

    @GetMapping("/{id}")
    public ResponseEntity<Biller> getBiller(@PathVariable("id") String id) throws Exception {
        return listBillersUseCase
                .getBiller(id)
                .map(biller -> ResponseEntity.ok(Mapper.BILLER_MAPPER.toBillerResp(biller)))
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/payment/review")
    public PaymentReviewResponse generatePaymentId() {
        return Mapper.PAYMENT_REVIEW_MAPPER.toPaymentReviewResp(
                paymentReviewUseCase.generatePaymentId());
    }

    @PostMapping("/validation")
    public ValidateRefNumberRequest.Resp validateRef(@RequestBody ValidateRefNumberCmd.Req node)
            throws Exception {
        return Mapper.VALIDATE_REF_NUMBER_MAPPER.toValidateRefNumberResp(
                validateCustomerRefUseCase.validate(node));
    }

    @PostMapping("/packages")
    public GetPackageRequest.Resp getBillerPackages(@RequestBody GetPackagesCmd.Req req)
            throws Exception {
        return Mapper.PACKAGE_MAPPER.toPackageResp(getPackagesUseCase.pullBillerPackages(req));
    }

    @GetMapping("/version")
    public Version getVersion() {
        return new Version(versionNumber);
    }
}
