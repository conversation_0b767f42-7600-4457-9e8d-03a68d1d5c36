package com.sbg.ug.optimus_backend.billing.orchastration;

import org.apache.commons.lang3.SerializationUtils;

public record TransactionResult(boolean success, Exception exception, TransactionContext context) {


    @Override
    public Exception exception() {
        if (exception == null) {
            return null;
        }
        return SerializationUtils.clone(exception);
    }

    // Override context accessor to return defensive copy using SerializationUtils.clone
    @Override
    public TransactionContext context() {
        if (context == null) {
            return null;
        }
        return SerializationUtils.clone(context);
    }

    // Factory methods
    public static TransactionResult success(TransactionContext context) {
        return new TransactionResult(true, null, context);
    }

    public static TransactionResult failure(Exception exception, TransactionContext context) {
        return new TransactionResult(false, exception, context);
    }
}
