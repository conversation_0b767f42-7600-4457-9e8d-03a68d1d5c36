package com.sbg.ug.optimus_backend.billing.external.http;

import com.sbg.ug.optimus_backend.apic.spi.ApicAuthSpi;
import com.sbg.ug.optimus_backend.billing.external.http.data.GetPackagesDto;
import com.sbg.ug.optimus_backend.billing.external.http.data.ListBillersDto;
import com.sbg.ug.optimus_backend.billing.external.http.data.NotifyBillerDto;
import com.sbg.ug.optimus_backend.billing.external.http.data.ValidateRefNumberDto;
import com.sbg.ug.optimus_backend.common.Constants;
import com.sbg.ug.optimus_backend.common.CryptoUtil;
import com.sbg.ug.optimus_backend.common.JsonUtil;
import com.sbg.ug.optimus_backend.exceptions.ErrorCode;
import com.sbg.ug.optimus_backend.exceptions.ServiceException;
import java.security.PrivateKey;
import java.util.List;
import kong.unirest.core.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class ApiConnectClient {

    public static final int BAD_REQUEST_HTTP_CODE = 400;
    public static final int SUCCESS_HTTP_CODE = 200;
    @Autowired private UnirestInstance unirest;

    @Autowired private PrivateKey privateKey;

    @Value("${api.connect.client_secret}")
    private String clientSecret;

    @Value("${api.connect.client_key}")
    private String clientKey;

    private final ApicAuthSpi apicAuthSpi;

    public String getToken() {
        return apicAuthSpi.getToken();
    }

    private static void assertSuccess(HttpResponse<?> resp) throws Exception {
        if (!resp.isSuccess()) {
            throw new ServiceException(
                    ErrorCode.Billing.THIRD_PARTY_ERROR, Constants.ErrorMessages.GenericError, 500);
        }
    }

    public ListBillersDto.Resp listBillers() throws Exception {
        try {
            var token = getToken();

            var resp =
                    unirest.get("/unified-billers/billers")
                            .accept(ContentType.APPLICATION_JSON)
                            .header(Constants.Headers.X_IBM_CLIENT_ID, clientKey)
                            .header(Constants.Headers.X_IBM_CLIENT_SECRET, clientSecret)
                            .header(
                                    Constants.Headers.AUTHORIZATION,
                                    Constants.Headers.BEARER + token)
                            .header(Constants.Headers.X_CHANNEL_ID, Constants.Headers.CHANNEL_ID)
                            .asObject(new GenericType<List<ListBillersDto.Biller>>() {});

            assertSuccess(resp);
            return new ListBillersDto.Resp(resp.getBody());
        } catch (Exception e) {
            throw new ServiceException(
                    ErrorCode.Billing.THIRD_PARTY_ERROR, Constants.ErrorMessages.GenericError, e);
        }
    }

    public ValidateRefNumberDto.Resp validateReferenceNumber(
            ValidateRefNumberDto.Req validationRequest) throws Exception {
        try {
            var token = getToken();
            var payload = JsonUtil.toJson(validationRequest);

            var resp =
                    unirest.post("/unified-billers/reference-query")
                            .contentType(ContentType.APPLICATION_JSON)
                            .accept(ContentType.APPLICATION_JSON)
                            .header(Constants.Headers.X_IBM_CLIENT_ID, clientKey)
                            .header("x-signature", CryptoUtil.sign(privateKey, payload))
                            .header(
                                    Constants.Headers.AUTHORIZATION,
                                    Constants.Headers.BEARER + token)
                            .header(Constants.Headers.X_CHANNEL_ID, Constants.Headers.CHANNEL_ID)
                            .body(payload)
                            .asObject(ValidateRefNumberDto.Resp.class);
            assertSuccess(resp);
            return resp.getBody();
        } catch (Exception e) {
            throw new ServiceException(
                    ErrorCode.Billing.THIRD_PARTY_ERROR,
                    Constants.ErrorMessages.ValidationError,
                    e);
        }
    }

    public GetPackagesDto.Resp getPackages(GetPackagesDto.Req req) throws Exception {
        try {
            var token = getToken();
            var payload = JsonUtil.toJson(req);

            var resp =
                    unirest.post("/unified-billers/packages")
                            .accept(ContentType.APPLICATION_JSON)
                            .contentType(ContentType.APPLICATION_JSON)
                            .header(
                                    Constants.Headers.AUTHORIZATION,
                                    Constants.Headers.BEARER + token)
                            .header(Constants.Headers.X_IBM_CLIENT_ID, clientKey)
                            .header("x-signature", CryptoUtil.sign(privateKey, payload))
                            .header(Constants.Headers.X_CHANNEL_ID, Constants.Headers.CHANNEL_ID)
                            .body(payload)
                            .asObject(GetPackagesDto.Resp.class);

            assertSuccess(resp);

            return resp.getBody();
        } catch (Exception e) {
            throw new ServiceException(
                    ErrorCode.Billing.THIRD_PARTY_ERROR, Constants.ErrorMessages.PackagesError, e);
        }
    }

    public NotifyBillerDto.Resp notifyBiller(NotifyBillerDto.Req req) throws Exception {
        try {
            var token = getToken();
            var payload = JsonUtil.toJson(req);

            var resp =
                    unirest.post("/unified-billers/payment-notification")
                            .accept(ContentType.APPLICATION_JSON)
                            .contentType(ContentType.APPLICATION_JSON)
                            .header(
                                    Constants.Headers.AUTHORIZATION,
                                    Constants.Headers.BEARER + token)
                            .header(Constants.Headers.X_IBM_CLIENT_ID, clientKey)
                            .header("x-signature", CryptoUtil.sign(privateKey, payload))
                            .header(Constants.Headers.X_CHANNEL_ID, Constants.Headers.CHANNEL_ID)
                            .body(payload)
                            .asObject(NotifyBillerDto.Resp.class);
            assertSuccess(resp);
            return resp.getBody();
        } catch (Exception e) {
            throw new ServiceException(
                    ErrorCode.Billing.PAYMENT_ERROR, Constants.ErrorMessages.PaymentFailed, e);
        }
    }
}
