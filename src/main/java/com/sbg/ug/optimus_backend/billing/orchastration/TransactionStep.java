package com.sbg.ug.optimus_backend.billing.orchastration;

import java.util.function.Consumer;
import java.util.function.Function;
import lombok.Getter;

@Getter
public class TransactionStep<T> {
    private final String name;
    private final DistributedAction<T> action;
    private final Function<TransactionContext, Boolean> condition;
    private final RetryPolicy retryPolicy;
    private final Consumer<Exception> errorHandler;

    private TransactionStep(Builder<T> builder) {
        this.name = builder.name;
        this.action = builder.action;
        this.condition = builder.condition;
        this.retryPolicy = builder.retryPolicy;
        this.errorHandler = builder.errorHandler;
    }

    public static class Builder<T> {
        private String name;
        private DistributedAction<T> action;
        private Function<TransactionContext, Boolean> condition = ctx -> true;
        private RetryPolicy retryPolicy = RetryPolicy.defaultPolicy();
        private Consumer<Exception> errorHandler = ex -> {};

        public Builder<T> name(String name) {
            this.name = name;
            return this;
        }

        public Builder<T> action(DistributedAction<T> action) {
            this.action = action;
            return this;
        }

        public Builder<T> condition(Function<TransactionContext, Boolean> condition) {
            this.condition = condition;
            return this;
        }

        public Builder<T> retryPolicy(RetryPolicy retryPolicy) {
            this.retryPolicy = retryPolicy;
            return this;
        }

        public Builder<T> onError(Consumer<Exception> errorHandler) {
            this.errorHandler = errorHandler;
            return this;
        }

        public TransactionStep<T> build() {
            return new TransactionStep<>(this);
        }
    }
}
