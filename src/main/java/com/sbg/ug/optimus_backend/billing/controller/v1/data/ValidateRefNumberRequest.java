package com.sbg.ug.optimus_backend.billing.controller.v1.data;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class ValidateRefNumberRequest {
    public record Req(
            String billerId,
            String customerReference,
            String flowId,
            String stepId,
            @JsonAnySetter Map<String, Object> otherFields) {
        public Req {
            otherFields = Map.copyOf(otherFields);
        }

        public Req(String billerId, String customerReference, String flowId, String stepId) {
            this(billerId, customerReference, flowId, stepId, Map.of());
        }
    }

    public record DisplayAmount(String label, BigDecimal amount) {}

    public record Resp(
            BigDecimal amount,
            String description,
            String currency,
            String variableAmount,
            Boolean isPaid,
            String validationId,
            BigDecimal outStandingAmount,
            List<DisplayAmount> charges) {
        public Resp {
            charges = List.copyOf(charges);
        }
    }
}
