package com.sbg.ug.optimus_backend.finacle.external.http;

import com.sbg.ug.optimus_backend.apic.spi.ApicAuthSpi;
import com.sbg.ug.optimus_backend.common.Constants;
import com.sbg.ug.optimus_backend.common.CryptoUtil;
import com.sbg.ug.optimus_backend.common.JsonUtil;
import com.sbg.ug.optimus_backend.exceptions.ErrorCode;
import com.sbg.ug.optimus_backend.exceptions.ServiceException;
import com.sbg.ug.optimus_backend.finacle.external.http.data.PayToAccountDto;
import com.sbg.ug.optimus_backend.finacle.external.http.data.PayToManyAccountsDto;
import java.security.PrivateKey;
import kong.unirest.core.ContentType;
import kong.unirest.core.HttpResponse;
import kong.unirest.core.UnirestInstance;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class ApicPaymentClient {
    public static final int BAD_REQUEST_HTTP_CODE = 400;
    public static final int SUCCESS_HTTP_CODE = 200;
    private final ApicAuthSpi apicAuthSpi;
    @Autowired private UnirestInstance unirest;
    @Autowired private PrivateKey privateKey;

    @Value("${api.connect.client_key}")
    private String clientKey;

    @Value("${api.connect.client_secret}")
    private String clientSecret;

    @Value("${api.connect.x_channel_id}")
    private String xChannelId;

    private static void assertSuccess(HttpResponse<?> resp) throws Exception {
        if (!resp.isSuccess()) {
            throw new ServiceException(
                    ErrorCode.Billing.THIRD_PARTY_ERROR, Constants.ErrorMessages.GenericError, 500);
        }
    }

    public PayToAccountDto.Resp payToAccount(PayToAccountDto.Req req) throws Exception {
        try {
            var token = apicAuthSpi.getToken();
            var payload = JsonUtil.toJson(req);
            var resp =
                    //                unirest.post("/e-pay/v2.0/payToAccount")
                    unirest.post("/agb-funds-transfer/v1.0.0/inhouse-transfer")
                            .accept(ContentType.APPLICATION_JSON)
                            .contentType(ContentType.APPLICATION_JSON)
                            .header(
                                    Constants.Headers.AUTHORIZATION,
                                    Constants.Headers.BEARER + token)
                            .header(Constants.Headers.X_IBM_CLIENT_ID, clientKey)
                            //  .header(Constants.Headers.X_IBM_CLIENT_SECRET,
                            // clientSecret)
                            .header(Constants.Headers.X_CHANNEL_ID, xChannelId)
                            .header("x-signature", CryptoUtil.sign(privateKey, payload))
                            .body(payload)
                            .asObject(PayToAccountDto.Resp.class);
            assertSuccess(resp);
            return resp.getBody();
        } catch (Exception e) {
            throw new ServiceException(
                    ErrorCode.Billing.FUNDS_TRANSFER_ERROR,
                    Constants.ErrorMessages.PaymentFailed,
                    e);
        }
    }

    public PayToManyAccountsDto.Resp payToManyAccounts(PayToManyAccountsDto.Req req)
            throws Exception {
        try {
            var token = apicAuthSpi.getToken();
            var payload = JsonUtil.toJson(req);
            var resp =
                    //
                    // unirest.post("/multiple-funds-transfer/v1.0.1/inhouse-transfer")
                    unirest.post("/funds-transfer/batch")
                            .accept(ContentType.APPLICATION_JSON)
                            .contentType(ContentType.APPLICATION_JSON)
                            .header(
                                    Constants.Headers.AUTHORIZATION,
                                    Constants.Headers.BEARER + token)
                            .header(Constants.Headers.X_IBM_CLIENT_ID, clientKey)
                            // .header(Constants.Headers.X_IBM_CLIENT_SECRET,
                            // clientSecret)
                            .header(Constants.Headers.X_CHANNEL_ID, xChannelId)
                            .header("x-signature", CryptoUtil.sign(privateKey, payload))
                            .body(payload)
                            .asObject(PayToManyAccountsDto.Resp.class);
            assertSuccess(resp);

            // TODO: Handle the case where charges are not posted
            // TODO : Get Transction Status
            // TODO : If not successful, we repost again

            return resp.getBody();
        } catch (Exception e) {
            throw new ServiceException(
                    ErrorCode.Billing.FUNDS_TRANSFER_ERROR,
                    Constants.ErrorMessages.PaymentFailed,
                    e);
        }
    }
}
