package com.sbg.ug.optimus_backend.finacle.usecase.data;

import java.util.List;
import org.springframework.modulith.NamedInterface;

public class PayToAccountCmd {

    private PayToAccountCmd() {}

    @NamedInterface
    public record Req(
            String messageId,
            String currency,
            String amount,
            String timestamp,
            Debit debit,
            List<Credit> creditList) {
        public Req {
            creditList = List.copyOf(creditList);
        }
    }

    @NamedInterface
    public record Debit(String accountNumber, String narration) {}

    @NamedInterface
    public record Credit(String accountNumber, String narration, String amount) {}

    @NamedInterface
    public record Resp(
            String statusCode,
            String description,
            String requestId,
            String responseDateTime,
            String transactionId) {}
}
