package com.sbg.ug.optimus_backend.finacle.usecase;

import com.sbg.ug.optimus_backend.finacle.external.http.ApicPaymentClient;
import com.sbg.ug.optimus_backend.finacle.usecase.data.PayToAccountCmd;
import com.sbg.ug.optimus_backend.finacle.usecase.data.PayToManyAccountsCmd;
import com.sbg.ug.optimus_backend.finacle.usecase.mappers.Mapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PayToAccountUseCase {

    private final ApicPaymentClient apiConnectClient;

    public PayToAccountCmd.Resp payToAccount(PayToAccountCmd.Req req) throws Exception {
        var resp = apiConnectClient.payToAccount(Mapper.PAY_TO_ACC.toReqDto(req));
        return Mapper.PAY_TO_ACC.fromDto(resp);
    }

    public PayToManyAccountsCmd.Resp payToManyAccounts(PayToManyAccountsCmd.Req req)
            throws Exception {
        var resp = apiConnectClient.payToManyAccounts(Mapper.PAY_TO_ACC.toManyAccountReqDto(req));

        return Mapper.PAY_TO_ACC.fromManyAccountDto(resp);
    }
}
