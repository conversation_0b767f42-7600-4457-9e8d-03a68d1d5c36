package com.sbg.ug.optimus_backend.finacle.usecase.mappers;

import com.sbg.ug.optimus_backend.finacle.external.http.data.PayToAccountDto;
import com.sbg.ug.optimus_backend.finacle.external.http.data.PayToManyAccountsDto;
import com.sbg.ug.optimus_backend.finacle.usecase.data.PayToAccountCmd;
import com.sbg.ug.optimus_backend.finacle.usecase.data.PayToManyAccountsCmd;
import org.mapstruct.factory.Mappers;

public class Mapper {

    private Mapper() {}

    public static final MoveFundCmdMapper PAY_TO_ACC = Mappers.getMapper(MoveFundCmdMapper.class);

    @org.mapstruct.Mapper
    public interface MoveFundCmdMapper {

        PayToAccountDto.Req toReqDto(PayToAccountCmd.Req cmd);

        PayToAccountCmd.Resp fromDto(PayToAccountDto.Resp resp);

        PayToManyAccountsDto.Req toManyAccountReqDto(PayToManyAccountsCmd.Req cmd);

        PayToManyAccountsCmd.Resp fromManyAccountDto(PayToManyAccountsDto.Resp resp);
    }
}
