package com.sbg.ug.optimus_backend.finacle.spi;

import com.sbg.ug.optimus_backend.finacle.usecase.PayToAccountUseCase;
import com.sbg.ug.optimus_backend.finacle.usecase.data.PayToAccountCmd;
import com.sbg.ug.optimus_backend.finacle.usecase.data.PayToManyAccountsCmd;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class FinaclePaymentSpi {

    private final PayToAccountUseCase payToAccountUseCase;

    public PayToAccountCmd.Resp payToAccount(PayToAccountCmd.Req req) throws Exception {
        return payToAccountUseCase.payToAccount(req);
    }

    public PayToManyAccountsCmd.Resp payToManyAccounts(PayToManyAccountsCmd.Req req)
            throws Exception {
        return payToAccountUseCase.payToManyAccounts(req);
    }
}
