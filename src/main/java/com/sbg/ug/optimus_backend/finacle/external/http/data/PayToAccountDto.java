package com.sbg.ug.optimus_backend.finacle.external.http.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PayToAccountDto {

    public record Req(
            String messageId,
            String currency,
            String amount,
            String timestamp,
            Debit debit,
            @JsonProperty("credit") List<Credit> creditList) {
        public Req {
            creditList = List.copyOf(creditList);
        }
    }

    public record Debit(String accountNumber, String narration) {}

    public record Credit(String accountNumber, String narration, String amount) {}

    public record Resp(
            String statusCode,
            String description,
            String requestId,
            String responseDateTime,
            String transactionId) {}
}
