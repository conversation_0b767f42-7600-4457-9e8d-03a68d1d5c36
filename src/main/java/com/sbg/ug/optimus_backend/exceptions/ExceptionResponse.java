package com.sbg.ug.optimus_backend.exceptions;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ExceptionResponse implements Serializable {
    private static final long serialVersionUID = -8381796825324221666L;

    private String code;
    private String message;
    private String responseType;
    private String additionalDetails;
}
