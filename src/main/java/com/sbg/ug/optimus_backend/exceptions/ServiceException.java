package com.sbg.ug.optimus_backend.exceptions;

import java.io.Serial;
import lombok.Getter;

@Getter
public class ServiceException extends Exception {

    @Serial private static final long serialVersionUID = 1L;
    private final ErrorCode code;
    private final Integer statusCode;

    public ServiceException(ErrorCode code, String message, Integer statusCode) {
        super(message);
        this.code = code;
        this.statusCode = statusCode;
    }

    public ServiceException(ErrorCode code, String message, Throwable cause, Integer statusCode) {
        super(message, cause);
        this.code = code;
        this.statusCode = statusCode;
    }

    public ServiceException(ErrorCode code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.statusCode = 500;
    }
}
