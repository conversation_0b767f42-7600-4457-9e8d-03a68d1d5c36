package com.sbg.ug.optimus_backend.exceptions.handlers;

import com.sbg.ug.optimus_backend.common.Constants;
import com.sbg.ug.optimus_backend.exceptions.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;

@Slf4j
@ControllerAdvice
public class ExceptionHandler {

    @org.springframework.web.bind.annotation.ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleException(Exception e) {
        log.error("Unexpected Exception", e);
        var response =
                new ErrorResponse(
                        ErrorCode.General.INTERNAL_SERVER_ERROR,
                        Constants.ErrorMessages.GenericError);
        return ResponseEntity.internalServerError().body(response);
    }

    @org.springframework.web.bind.annotation.ExceptionHandler(BusinessException.class)
    public ResponseEntity<ErrorResponse> handleBusinessException(BusinessException e) {

        log.error("Business Error Occurred: ", e);

        var response = new ErrorResponse(e.getCode(), e.getMessage());
        return ResponseEntity.status(400).body(response);
    }

    @org.springframework.web.bind.annotation.ExceptionHandler(ServiceException.class)
    public ResponseEntity<ErrorResponse> handleServiceException(ServiceException e) {
        log.error("Optimus Server Error Occurred: ", e);
        var response = new ErrorResponse(e.getCode(), e.getMessage());
        return ResponseEntity.status(500).body(response);
    }

    public record ErrorResponse(ErrorCode code, String message) {}
}
