package com.sbg.ug.optimus_backend.exceptions;

public interface ErrorCode {

    enum Billing implements ErrorCode {
        MISSING_FIELD,
        UNSUPPORTED_FIELD,
        BILLER_NOT_FOUND,
        B<PERSON><PERSON><PERSON>_FLOW_NOT_FOUND,
        B<PERSON><PERSON><PERSON>_STEP_NOT_FOUND,
        THIRD_PARTY_ERROR,
        DATABASE_ERROR,
        INCORRECT_OTP,
        FUNDS_TRANSFER_ERROR,
        PAYMENT_ERROR,
        OTP_VALIDATION_ERROR,
        NO_MORE_OTP_ATTEMPTS,
        OTP_EXPIRED
    }

    enum General implements ErrorCode {
        INTERNAL_SERVER_ERROR,
    }
}
